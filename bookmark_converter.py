#!/usr/bin/env python3
"""
Chrome书签HTML转JSON转换器
将Chrome导出的书签HTML文件转换为适合导入其他项目的JSON格式
"""

import json
import re
import hashlib
from datetime import datetime
from urllib.parse import urlparse
from bs4 import BeautifulSoup
import argparse
import sys
from pathlib import Path


class BookmarkConverter:
    def __init__(self):
        self.bookmarks = []
        self.categories = {}
        self.folder_stack = []
        
    def parse_html_file(self, file_path):
        """解析Chrome书签HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()

        soup = BeautifulSoup(content, 'html.parser')

        # 查找所有DT元素并直接处理
        dt_elements = soup.find_all('dt')
        current_path = []

        for dt in dt_elements:
            # 检查是否是文件夹
            h3 = dt.find('h3')
            if h3:
                # 这是一个文件夹
                folder_name = h3.get_text().strip()
                # 简单的路径管理 - 根据文件夹层级确定路径
                if folder_name == '书签栏':
                    current_path = [folder_name]
                elif folder_name in ['电商', 'Geek', '物流']:  # 主要分类
                    current_path = ['书签栏', folder_name]
                else:  # 子分类
                    if len(current_path) >= 2:
                        current_path = current_path[:2] + [folder_name]
                    else:
                        current_path.append(folder_name)

                # 记录文件夹信息
                if folder_name not in self.categories:
                    self.categories[folder_name] = {
                        'name': folder_name,
                        'parent': current_path[-2] if len(current_path) > 1 else None,
                        'path': current_path.copy(),
                        'bookmark_count': 0
                    }
            else:
                # 这是一个书签
                a_tag = dt.find('a')
                if a_tag:
                    bookmark = self._extract_bookmark_info(a_tag, current_path)
                    if bookmark:
                        self.bookmarks.append(bookmark)
                        # 更新分类计数
                        if current_path:
                            category_name = current_path[-1]
                            if category_name in self.categories:
                                self.categories[category_name]['bookmark_count'] += 1

        return self.bookmarks, self.categories
    
    def _parse_dl_element(self, dl_element, parent_path=[]):
        """递归解析DL元素"""
        current_folder = None
        i = 0
        children = list(dl_element.children)

        while i < len(children):
            child = children[i]
            if hasattr(child, 'name') and child.name == 'dt':
                # 检查是否是文件夹
                h3 = child.find('h3')
                if h3:
                    # 这是一个文件夹
                    folder_name = h3.get_text().strip()
                    current_folder = folder_name
                    folder_path = parent_path + [folder_name]

                    # 记录文件夹信息
                    if folder_name not in self.categories:
                        self.categories[folder_name] = {
                            'name': folder_name,
                            'parent': parent_path[-1] if parent_path else None,
                            'path': folder_path,
                            'bookmark_count': 0
                        }

                    # 查找下一个DL元素（子文件夹内容）
                    j = i + 1
                    while j < len(children):
                        next_child = children[j]
                        if hasattr(next_child, 'name') and next_child.name == 'dl':
                            self._parse_dl_element(next_child, folder_path)
                            break
                        j += 1
                else:
                    # 这是一个书签
                    a_tag = child.find('a')
                    if a_tag:
                        bookmark = self._extract_bookmark_info(a_tag, parent_path)
                        if bookmark:
                            self.bookmarks.append(bookmark)
                            # 更新分类计数
                            if parent_path:
                                category_name = parent_path[-1]
                                if category_name in self.categories:
                                    self.categories[category_name]['bookmark_count'] += 1
            i += 1
    
    def _extract_bookmark_info(self, a_tag, folder_path):
        """提取单个书签信息"""
        try:
            url = a_tag.get('href', '').strip()
            title = a_tag.get_text().strip()
            add_date = a_tag.get('add_date', '')
            icon = a_tag.get('icon', '')
            
            if not url or not title:
                return None
            
            # 生成唯一ID
            bookmark_id = hashlib.md5(f"{url}{title}".encode()).hexdigest()[:12]
            
            # 转换时间戳
            date_added = self._convert_timestamp(add_date)
            
            # 提取域名作为标签
            tags = self._extract_tags(url, title)
            
            # 构建书签对象
            bookmark = {
                'id': bookmark_id,
                'title': title,
                'url': url,
                'description': self._generate_description(title, url),
                'category': folder_path[-1] if folder_path else 'Uncategorized',
                'tags': tags,
                'date_added': date_added,
                'folder_path': folder_path,
                'domain': urlparse(url).netloc
            }
            
            # 可选：包含图标
            if icon and icon.startswith('data:image'):
                bookmark['icon'] = icon
            
            return bookmark
            
        except Exception as e:
            print(f"Error processing bookmark: {e}")
            return None
    
    def _convert_timestamp(self, timestamp_str):
        """转换Unix时间戳为ISO格式"""
        try:
            if timestamp_str:
                timestamp = int(timestamp_str)
                dt = datetime.fromtimestamp(timestamp)
                return dt.isoformat() + 'Z'
        except (ValueError, OSError):
            pass
        return datetime.now().isoformat() + 'Z'
    
    def _extract_tags(self, url, title):
        """从URL和标题中提取标签"""
        tags = []
        
        # 从域名提取
        domain = urlparse(url).netloc.lower()
        if 'tiktok' in domain:
            tags.append('TikTok')
        if 'github' in domain:
            tags.append('GitHub')
        if 'linux.do' in domain:
            tags.append('Linux')
        
        # 从标题提取关键词
        title_lower = title.lower()
        keywords = ['电商', 'tiktok', 'api', 'github', 'ai', '工具', '教程', '开源']
        for keyword in keywords:
            if keyword in title_lower:
                tags.append(keyword.capitalize())
        
        return list(set(tags))  # 去重
    
    def _generate_description(self, title, url):
        """生成描述"""
        domain = urlparse(url).netloc
        return f"来自 {domain} 的书签"
    
    def export_to_json(self, output_file, include_icons=False, format_type='full'):
        """导出为JSON格式"""
        
        # 准备元数据
        metadata = {
            'source': 'Chrome Bookmarks',
            'export_date': datetime.now().isoformat() + 'Z',
            'total_bookmarks': len(self.bookmarks),
            'total_categories': len(self.categories),
            'format_version': '1.0',
            'include_icons': include_icons
        }
        
        # 处理书签数据
        processed_bookmarks = []
        for bookmark in self.bookmarks:
            processed_bookmark = bookmark.copy()
            
            # 根据选项处理图标
            if not include_icons and 'icon' in processed_bookmark:
                del processed_bookmark['icon']
            
            processed_bookmarks.append(processed_bookmark)
        
        # 根据格式类型组织数据
        if format_type == 'full':
            output_data = {
                'metadata': metadata,
                'bookmarks': processed_bookmarks,
                'categories': list(self.categories.values())
            }
        elif format_type == 'simple':
            output_data = {
                'bookmarks': [
                    {
                        'title': b['title'],
                        'url': b['url'],
                        'category': b['category'],
                        'tags': b['tags']
                    }
                    for b in processed_bookmarks
                ]
            }
        elif format_type == 'grouped':
            # 按分类分组
            grouped = {}
            for bookmark in processed_bookmarks:
                category = bookmark['category']
                if category not in grouped:
                    grouped[category] = []
                grouped[category].append(bookmark)
            
            output_data = {
                'metadata': metadata,
                'bookmarks_by_category': grouped
            }
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        return output_data


def main():
    parser = argparse.ArgumentParser(description='Convert Chrome bookmarks HTML to JSON')
    parser.add_argument('input_file', help='Input HTML bookmark file')
    parser.add_argument('-o', '--output', default='bookmarks.json', help='Output JSON file')
    parser.add_argument('--include-icons', action='store_true', help='Include base64 icons')
    parser.add_argument('--format', choices=['full', 'simple', 'grouped'], 
                       default='full', help='Output format type')
    
    args = parser.parse_args()
    
    if not Path(args.input_file).exists():
        print(f"Error: Input file '{args.input_file}' not found")
        sys.exit(1)
    
    print(f"Converting {args.input_file} to JSON...")
    
    converter = BookmarkConverter()
    bookmarks, categories = converter.parse_html_file(args.input_file)
    
    print(f"Found {len(bookmarks)} bookmarks in {len(categories)} categories")
    
    # 导出JSON
    output_data = converter.export_to_json(
        args.output, 
        include_icons=args.include_icons,
        format_type=args.format
    )
    
    print(f"Successfully exported to {args.output}")
    print(f"Format: {args.format}")
    print(f"Include icons: {args.include_icons}")
    
    # 显示分类统计
    print("\nCategories found:")
    for category in categories.values():
        print(f"  - {category['name']}: {category['bookmark_count']} bookmarks")


if __name__ == '__main__':
    main()
