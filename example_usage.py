#!/usr/bin/env python3
"""
JSON书签数据使用示例
演示如何使用转换后的JSON书签数据
"""

import json
from collections import Counter
from urllib.parse import urlparse

def load_bookmarks(json_file):
    """加载JSON书签数据"""
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_bookmarks(data):
    """分析书签数据"""
    bookmarks = data.get('bookmarks', [])
    
    print(f"=== 书签数据分析 ===")
    print(f"总书签数: {len(bookmarks)}")
    
    # 分类统计
    categories = Counter(b['category'] for b in bookmarks)
    print(f"\n=== 分类统计 ===")
    for category, count in categories.most_common():
        print(f"{category}: {count}个")
    
    # 域名统计
    domains = Counter(b['domain'] for b in bookmarks)
    print(f"\n=== 热门域名 TOP 10 ===")
    for domain, count in domains.most_common(10):
        print(f"{domain}: {count}个")
    
    # 标签统计
    all_tags = []
    for bookmark in bookmarks:
        all_tags.extend(bookmark.get('tags', []))
    
    tag_counts = Counter(all_tags)
    print(f"\n=== 热门标签 TOP 10 ===")
    for tag, count in tag_counts.most_common(10):
        print(f"{tag}: {count}次")

def search_bookmarks(data, keyword):
    """搜索书签"""
    bookmarks = data.get('bookmarks', [])
    results = []
    
    keyword_lower = keyword.lower()
    for bookmark in bookmarks:
        if (keyword_lower in bookmark['title'].lower() or 
            keyword_lower in bookmark['url'].lower() or
            keyword_lower in bookmark.get('category', '').lower()):
            results.append(bookmark)
    
    return results

def filter_by_category(data, category):
    """按分类筛选书签"""
    bookmarks = data.get('bookmarks', [])
    return [b for b in bookmarks if b['category'] == category]

def export_category_html(data, category, output_file):
    """导出特定分类为HTML"""
    bookmarks = filter_by_category(data, category)
    
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{category} 书签</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .bookmark {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
        .title {{ font-weight: bold; color: #0066cc; }}
        .url {{ color: #666; font-size: 0.9em; }}
        .tags {{ color: #999; font-size: 0.8em; }}
    </style>
</head>
<body>
    <h1>{category} 书签 ({len(bookmarks)}个)</h1>
"""
    
    for bookmark in bookmarks:
        tags_str = ', '.join(bookmark.get('tags', []))
        html_content += f"""
    <div class="bookmark">
        <div class="title"><a href="{bookmark['url']}" target="_blank">{bookmark['title']}</a></div>
        <div class="url">{bookmark['url']}</div>
        <div class="tags">标签: {tags_str}</div>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"已导出 {category} 分类的 {len(bookmarks)} 个书签到 {output_file}")

def main():
    # 加载数据
    data = load_bookmarks('bookmarks_full.json')
    
    # 分析数据
    analyze_bookmarks(data)
    
    # 搜索示例
    print(f"\n=== 搜索 'TikTok' ===")
    tiktok_results = search_bookmarks(data, 'TikTok')
    print(f"找到 {len(tiktok_results)} 个相关书签")
    for result in tiktok_results[:5]:  # 显示前5个
        print(f"- {result['title'][:50]}...")
    
    # 导出TikTok分类为HTML
    export_category_html(data, 'Tiktok', 'tiktok_bookmarks.html')
    
    # 导出AI分类为HTML
    export_category_html(data, 'AI', 'ai_bookmarks.html')
    
    print(f"\n=== 使用建议 ===")
    print("1. 可以将JSON数据导入到数据库中")
    print("2. 可以构建Web界面进行搜索和管理")
    print("3. 可以定期更新和同步书签")
    print("4. 可以基于标签构建推荐系统")

if __name__ == '__main__':
    main()
