{"metadata": {"source": "Chrome Bookmarks", "export_date": "2025-07-28T10:08:58.261215Z", "total_bookmarks": 587, "total_categories": 15, "format_version": "1.0", "include_icons": false}, "bookmarks_by_category": {"Tiktok": [{"id": "fd51a979c20d", "title": "FastMoss-TikTok短视频&直播电商与达人营销数据分析 - FastMoss数据,有乐今天", "url": "https://www.fastmoss.com/zh/e-commerce/search", "description": "来自 www.fastmoss.com 的书签", "category": "Tiktok", "tags": ["电商", "Tiktok"], "date_added": "2024-07-15T11:19:26Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.fastmoss.com"}, {"id": "b377f91d5a28", "title": "搜索 TikTok 商品", "url": "https://didadog.com/libraryProduct/search", "description": "来自 didadog.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-07-15T11:19:41Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "didadog.com"}, {"id": "b870ab1d2504", "title": "TikTok Business Center", "url": "https://business.tiktok.com/manage/overview?org_id=7392109665901068289&detail_adv=0&filters=3,1,2,4,5&selectAccountType=1", "description": "来自 business.tiktok.com 的书签", "category": "Tiktok", "tags": ["TikTok", "Tiktok"], "date_added": "2024-07-16T18:16:55Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "business.tiktok.com"}, {"id": "6e92f6555974", "title": "TikTok Creator Marketplace", "url": "https://creatormarketplace.tiktok.com/ad/dashboard", "description": "来自 creatormarketplace.tiktok.com 的书签", "category": "Tiktok", "tags": ["TikTok", "Tiktok"], "date_added": "2024-08-01T17:37:29Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "creatormarketplace.tiktok.com"}, {"id": "559c44e9262f", "title": "主页 - 美容时尚", "url": "https://beautystyles.ch/", "description": "来自 beautystyles.ch 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-18T11:40:53Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "beautystyles.ch"}, {"id": "b32fc3c83441", "title": "一文看懂TikTok Shop全托管模式 - TikTok Shop全托管卖家中心学习平台", "url": "https://learning-center.fanczs.com/support/content/138103?graphId=654&hideFooter=1&hideUnify=1&mappingType=2&pageId=441&spaceId=235×tamp=1721292019077", "description": "来自 learning-center.fanczs.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-07-18T16:40:33Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "learning-center.fanczs.com"}, {"id": "02dcf2cac40e", "title": "TK导航 - ImTiktoker 玩家网", "url": "https://imtiktoker.com/", "description": "来自 imtiktoker.com 的书签", "category": "Tiktok", "tags": ["TikTok", "Tiktok"], "date_added": "2024-07-18T17:33:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "imtiktoker.com"}, {"id": "4af1d55481f8", "title": "全部商品页", "url": "https://shop77l302963o247.1688.com/page/offerlist.htm?spm=a2615.12330364.wp_pc_common_topnav.0", "description": "来自 shop77l302963o247.1688.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-18T18:49:15Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "shop77l302963o247.1688.com"}, {"id": "25bc170b6a5f", "title": "‬﻿⁣﻿⁢⁣‍⁣⁣​⁤​⁡​‬​⁡⁣‌‍‬‬​‌⁤⁢‬⁣‍​‬⁤⁡⁤​﻿⁡⁣﻿‬⁣‬‌⁡​​⁣‌商家建联达人合作指南 - Feishu Docs", "url": "https://w6l1a2bce9.feishu.cn/docx/F8Fkd7bpioatsZx9ztrc1w57nad", "description": "来自 w6l1a2bce9.feishu.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-26T08:51:48Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "w6l1a2bce9.feishu.cn"}, {"id": "713a1f934995", "title": "话术管理", "url": "https://www.feiyudaren.com/chatTmp", "description": "来自 www.feiyudaren.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-08-01T16:12:43Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.feiyudaren.com"}, {"id": "18fab89d7d29", "title": "免费开源神器：一键分发，自动化短视频上传，支持主流个自媒体平台，矩阵化运营 - 掘金", "url": "https://juejin.cn/post/7372114027840208911", "description": "来自 juejin.cn 的书签", "category": "Tiktok", "tags": ["开源"], "date_added": "2024-08-01T17:47:33Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "juejin.cn"}, {"id": "82c848c10e85", "title": "TikTok Creator Marketplace", "url": "https://creatormarketplace.tiktok.com/ad/market", "description": "来自 creatormarketplace.tiktok.com 的书签", "category": "Tiktok", "tags": ["TikTok", "Tiktok"], "date_added": "2024-08-02T08:34:09Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "creatormarketplace.tiktok.com"}, {"id": "011ea9a6627d", "title": "TikTok数据分析（TikTok Analytics）终极使用指南 | tk0123跨境电商导航", "url": "https://www.tk0123.com/444.html", "description": "来自 www.tk0123.com 的书签", "category": "Tiktok", "tags": ["电商", "Tiktok"], "date_added": "2024-08-02T14:48:57Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.tk0123.com"}, {"id": "fb3b47c3e109", "title": "Tiktok 广告投放花了 150 万 RMB 得出来的经验 - 宇周博客", "url": "https://yuzhoublog.com/tiktok-advertising/", "description": "来自 yuzhoublog.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-08-05T15:31:32Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "yuzhoublog.com"}, {"id": "5cceee56105f", "title": "美国tiktok千粉号购买-tiktok美国满月千粉白号批发-TikTok千粉账号购买平台", "url": "https://www.tiktokfensi.com/tiktokqianfen/meiguo/", "description": "来自 www.tiktokfensi.com 的书签", "category": "Tiktok", "tags": ["TikTok", "Tiktok"], "date_added": "2024-08-09T15:49:56Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.tiktokfensi.com"}, {"id": "3c1698ac86f2", "title": "jewvfRf01/TikTok-viewbot: 🔥 tiktok viewbot 500+ per second 🔥 tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot ztepgr", "url": "https://github.com/jewvfRf01/TikTok-viewbot", "description": "来自 github.com 的书签", "category": "Tiktok", "tags": ["Tiktok", "GitHub"], "date_added": "2024-09-10T11:29:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "github.com"}, {"id": "b38004009af3", "title": "TikTokMod - OpenDesktop.org", "url": "https://www.opendesktop.org/p/1515346/", "description": "来自 www.opendesktop.org 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-09-12T14:39:48Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.opendesktop.org"}, {"id": "f1274546baca", "title": "【Tiktok分享】商品卡截流玩法，对应资源体系搭建 - 知无不言跨境电商社区", "url": "https://www.wearesellers.com/question/85838", "description": "来自 www.wearesellers.com 的书签", "category": "Tiktok", "tags": ["电商", "Tiktok"], "date_added": "2024-09-24T16:22:25Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.wearesellers.com"}, {"id": "89841cc5f4e5", "title": "刚刚【social-auto-upload】更新【快手】的支持：免费开源自媒体视频一键分发，自动化上传 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/173129", "description": "来自 linux.do 的书签", "category": "Tiktok", "tags": ["Linux", "开源"], "date_added": "2024-09-21T12:56:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "linux.do"}, {"id": "dc8312555710", "title": "National Drug Code Directory", "url": "https://dps.fda.gov/ndc/searchresult?selection=finished_product&content=PROPRIETARYNAME&type=SADOER+", "description": "来自 dps.fda.gov 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-09-27T17:31:13Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "dps.fda.gov"}, {"id": "43d4d14ca2d8", "title": "产品 - OMS", "url": "https://oms-cntodd.xlwms.com/product/list", "description": "来自 oms-cntodd.xlwms.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-09-29T09:31:26Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "oms-cntodd.xlwms.com"}, {"id": "bd5328164d34", "title": "培训资料", "url": "https://drive.weixin.qq.com/s?k=ADYAZwfOAFMLfjfQSt#/?folderId=i.1970326243010358.1688856698597697_d.723012717gO1n", "description": "来自 drive.weixin.qq.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-09-30T10:36:02Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "drive.weixin.qq.com"}, {"id": "79b8aec9480e", "title": "⁣‬​‌‌⁤⁣⁤‍⁡​​‍​‍﻿﻿⁢​⁣⁢‍⁤‍⁣​​‍‍​‬‬‍‌​​​‬⁢⁣​﻿⁢⁣‬‍⁡‬﻿Engu- BC注册新 - 飞书云文档", "url": "https://eg88888888.feishu.cn/slides/EKfysBJselFLKNdYu7Zc5z3Qnob?chunked=false", "description": "来自 eg88888888.feishu.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-10-25T15:05:14Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "eg88888888.feishu.cn"}, {"id": "abf7fabe632d", "title": "TikTok专业服务平台", "url": "https://vm.ttsop.net/dashboard/tiktok-qf-account/purchase", "description": "来自 vm.ttsop.net 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-11-06T13:00:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "vm.ttsop.net"}, {"id": "b56c400f3bd5", "title": "如何一键使用 ComfyUI 进行本地 FLUX.1 LoRA 的训练_comfyui-fluxtrainer-CSDN博客", "url": "https://blog.csdn.net/m0_71746299/article/details/*********", "description": "来自 blog.csdn.net 的书签", "category": "Tiktok", "tags": ["Ai"], "date_added": "2024-12-19T01:39:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "blog.csdn.net"}, {"id": "65db09a2afc6", "title": "⁣‍​‍﻿‍﻿​⁡‍⁢​⁣⁢⁤​‌​⁢﻿⁤‌​⁡⁤‌​⁤⁣⁢‌﻿‍​﻿⁢​​‬﻿⁡﻿‍​﻿​﻿‌⁢跨境密码-TikTok行业资源汇总 - 飞书云文档", "url": "https://b5dqpyykxn.feishu.cn/docx/SdTDd41U8oB2CZxGMtgcxUflnDc", "description": "来自 b5dqpyykxn.feishu.cn 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-12-24T16:22:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "b5dqpyykxn.feishu.cn"}, {"id": "19fdea07a9ee", "title": "新卖家推荐费促销 - 2025 --- New Seller Referral Fee Promotion - 2025", "url": "https://seller-us.tiktok.com/university/essay?knowledge_id=1557461786560302&role=1&course_type=1&from=search&identity=1", "description": "来自 seller-us.tiktok.com 的书签", "category": "Tiktok", "tags": ["TikTok"], "date_added": "2025-01-03T10:12:03Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "seller-us.tiktok.com"}, {"id": "79d46d6eb93e", "title": "TikTok干货资料_TikTok资料专区_运营学习手册免费领取-TKTOC运营导航", "url": "https://www.tktoc.com/docs/document", "description": "来自 www.tktoc.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-01-03T14:16:39Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.tktoc.com"}, {"id": "dbeb2007c784", "title": "Give Me Star·东南亚测评专家", "url": "https://po.givemestar.com/task/dashboard", "description": "来自 po.givemestar.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-01-03T18:33:00Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "po.givemestar.com"}, {"id": "722bc8d0305f", "title": "出海匠 - TikTok达人搜索", "url": "https://www.chuhaijiang.com/tiktok/entity/tiktok_creator/search?is_has_product=488a6ae15b2b707bf240132069174fce", "description": "来自 www.chuhaijiang.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-01-04T10:57:15Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.chuhaijiang.com"}, {"id": "46d5c2327c4f", "title": "TikTok 带货达人广场丨嘀嗒狗", "url": "https://didadog.com/talent/square", "description": "来自 didadog.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-01-10T09:48:13Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "didadog.com"}, {"id": "33edeeca13f0", "title": "Loukious/StreamLabsTikTokStreamKeyGenerator: TikTok Live Stream Key Generator for OBS Studio using Streamlabs API", "url": "https://github.com/Loukious/StreamLabsTikTokStreamKeyGenerator?tab=readme-ov-file", "description": "来自 github.com 的书签", "category": "Tiktok", "tags": ["Tiktok", "GitHub", "Api"], "date_added": "2025-01-23T15:22:27Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "github.com"}, {"id": "af954dcafb96", "title": "TikTok专业服务平台", "url": "https://vm.ttsop.cn/dashboard", "description": "来自 vm.ttsop.cn 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-01-24T10:03:56Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "vm.ttsop.cn"}, {"id": "44d2c98864db", "title": "参与电子收据易 2.0 的确认表格 --- แบบฟอร์มการยืนยันในการเข้าร่วม Easy E-Receipt 2.0", "url": "https://bytedance.sg.larkoffice.com/share/base/form/shrlg1B5cSwJ008wewFYON5EnMf", "description": "来自 bytedance.sg.larkoffice.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-02-07T17:03:34Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bytedance.sg.larkoffice.com"}, {"id": "4b778c3efe07", "title": "一、在TikTok如何建立自己的达人分销私域 - 飞书云文档", "url": "https://ia8xe4wnh3u.feishu.cn/docx/T7kAdZxCKo066WxgU8Hc3nt1nif", "description": "来自 ia8xe4wnh3u.feishu.cn 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-03-02T15:06:21Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "ia8xe4wnh3u.feishu.cn"}, {"id": "ddc7609f2458", "title": "Docs", "url": "https://b5dqpyykxn.feishu.cn/drive/folder/MHYdfCj71lhFV2dGq2zckc66nAb", "description": "来自 b5dqpyykxn.feishu.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-03-02T15:06:29Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "b5dqpyykxn.feishu.cn"}, {"id": "07e00cda949a", "title": "Trademark", "url": "https://www.ipthailand.go.th/en/trademark-001.html", "description": "来自 www.ipthailand.go.th 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-03-26T10:05:32Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.ipthailand.go.th"}, {"id": "446308577719", "title": "关于 TikTok 上的全漏斗营销 | TikTok 广告管理平台", "url": "https://ads.tiktok.com/help/article/full-funnel-marketing-tiktok?lang=zh", "description": "来自 ads.tiktok.com 的书签", "category": "Tiktok", "tags": ["TikTok", "Tiktok"], "date_added": "2025-03-27T15:55:29Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "ads.tiktok.com"}, {"id": "c8ea081e1f8d", "title": "TikTok Shop - Marketplace Sellers Commission Fee Rate, from February 1st 2024.pdf", "url": "file:///F:/Windows_data/Downloads/TikTok%20Shop%20-%20Marketplace%20Sellers%20Commission%20Fee%20Rate,%20from%20February%201st%202024.pdf", "description": "来自  的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-04-12T17:19:55Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": ""}, {"id": "b61ad540a49a", "title": "东南亚跨境佣金及交易手续费规则总览", "url": "https://seller.tiktokglobalshop.com/university/essay?knowledge_id=6173886299686658&role=1&course_type=1&from=search&identity=1", "description": "来自 seller.tiktokglobalshop.com 的书签", "category": "Tiktok", "tags": ["TikTok"], "date_added": "2025-04-12T17:26:52Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "seller.tiktokglobalshop.com"}, {"id": "c5e0b22533fb", "title": "在线下载高清质量的没有徽标、水印、watermark 的 TikTok 视频 | TikTok 视频下载器 - TikVid.io", "url": "https://tikvid.io/zh-cn", "description": "来自 tikvid.io 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2025-05-08T09:24:19Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "tikvid.io"}, {"id": "cbbec23d2403", "title": "淘数据", "url": "https://www.taosj.com/", "description": "来自 www.taosj.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.taosj.com"}, {"id": "89b5f511f69e", "title": "国内国外邮编查询", "url": "https://www.nowmsg.com/", "description": "来自 www.nowmsg.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.nowmsg.com"}, {"id": "f9cb64ac9111", "title": "Pigcha加速器官网", "url": "http://pigcha.com/", "description": "来自 pigcha.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "pigcha.com"}, {"id": "f10aab776a18", "title": "公章专家_公司电子印章图片生成器_印章在线制作大师", "url": "http://seal.ssjjss.com/", "description": "来自 seal.ssjjss.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "seal.ssjjss.com"}, {"id": "148664e1b400", "title": "全球付-国际购物-在线消费新体验", "url": "https://www.globalcash.hk/v4/dashboard", "description": "来自 www.globalcash.hk 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.globalcash.hk"}, {"id": "f50cbbac015f", "title": "TREND HUNTER - #1 in Trends, Trend Reports, Fashion Trends, Tech, Design", "url": "https://www.trendhunter.com/", "description": "来自 www.trendhunter.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.trendhunter.com"}, {"id": "ea7b87057b49", "title": "马帮ERP", "url": "https://900541.private.mabangerp.com/", "description": "来自 900541.private.mabangerp.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "900541.private.mabangerp.com"}, {"id": "e6909802e6ac", "title": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "url": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "description": "来自 oms.yunexpress.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "oms.yunexpress.cn"}, {"id": "f1cc5b55760f", "title": "燕文物流", "url": "https://portal.yw56.com.cn/systemManagement/userMaintenance", "description": "来自 portal.yw56.com.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "portal.yw56.com.cn"}, {"id": "8e2919f66d0c", "title": "AMZ123亚马逊导航-跨境电商出海门户", "url": "https://www.amz123.com/", "description": "来自 www.amz123.com 的书签", "category": "Tiktok", "tags": ["电商"], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.amz123.com"}, {"id": "0efab887d440", "title": "跨境电商语言解决方案", "url": "https://www.alifanyi.com/index.html?spm=a271w.8016606.0.0.12525e88rilcIf", "description": "来自 www.alifanyi.com 的书签", "category": "Tiktok", "tags": ["电商"], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.alifanyi.com"}, {"id": "47a3a82cbdaf", "title": "在线制作电子公章(专业的免费公章在线生成工具)—制图网", "url": "http://www.makepic.net/tool/signet.html", "description": "来自 www.makepic.net 的书签", "category": "Tiktok", "tags": ["工具"], "date_added": "2023-02-28T21:15:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.makepic.net"}, {"id": "1d590bde6459", "title": "Fiverr - Freelance Services Marketplace", "url": "https://www.fiverr.com/?source=top_nav", "description": "来自 www.fiverr.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-08-04T14:50:53Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.fiverr.com"}, {"id": "ce29ebd43ffe", "title": "VQCM（微企传媒）推广资源报价表", "url": "https://ud8dvlo0p7.jiandaoyun.com/dash/64ae640e95c6cf0009ff617d", "description": "来自 ud8dvlo0p7.jiandaoyun.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-08-10T11:06:40Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "ud8dvlo0p7.jiandaoyun.com"}, {"id": "5d8b075c0c04", "title": "微企通-站外推广报价", "url": "https://shimo.im/sheets/vJVRCvCyPkxHdg3q", "description": "来自 shimo.im 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-08-18T09:40:08Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "shimo.im"}, {"id": "82d2807cfda5", "title": "逮虾录 - 资源列表", "url": "https://daixialu.com/resource", "description": "来自 daixialu.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-08-18T10:02:08Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "daixialu.com"}, {"id": "ef35732bcc6f", "title": "PNG素材网-免费高清透明PNG素材资源分享网站_PNG图片素材下载 PngSucai.Com", "url": "https://www.pngsucai.com/", "description": "来自 www.pngsucai.com 的书签", "category": "Tiktok", "tags": ["Ai"], "date_added": "2023-08-26T23:16:43Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.pngsucai.com"}, {"id": "2e560de2ad8b", "title": "各国插座规格简介 - 创客出手", "url": "https://makeronsite.com/plug_types.html", "description": "来自 makeronsite.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-08-29T15:08:05Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "makeronsite.com"}, {"id": "262d9b149b52", "title": "站外查询 - 站外智汇", "url": "https://www.trafficwiser.com/dealsite", "description": "来自 www.trafficwiser.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-09-06T19:33:09Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.trafficwiser.com"}, {"id": "1b9d627b55e2", "title": "Fake Name Generator", "url": "https://namefake.com/", "description": "来自 namefake.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-09-13T13:53:24Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "namefake.com"}, {"id": "db363e8d26b1", "title": "发起的举报", "url": "https://csp.aliexpress.com/m_apps/violation/pop-rptinitiated?channelId=581345", "description": "来自 csp.aliexpress.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-09-27T11:46:12Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "csp.aliexpress.com"}, {"id": "5d16d82ec3d4", "title": "跨境小白 - 跨境电商公司评价网", "url": "https://kjrate.com/", "description": "来自 kjrate.com 的书签", "category": "Tiktok", "tags": ["电商"], "date_added": "2024-03-20T23:55:42Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "kjrate.com"}, {"id": "96d9f8c7e438", "title": "法国Cdiscount平台导表批量上传产品 · 跨境erp3.0使用帮助手册 · 看云", "url": "https://www.kancloud.cn/hellmen/erp3/2375856", "description": "来自 www.kancloud.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-04-02T12:01:13Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.kancloud.cn"}, {"id": "17649c38c7c6", "title": "福步外贸论坛(FOB Business Forum) |中国第一外贸论坛", "url": "https://bbs.fobshanghai.com/index.php", "description": "来自 bbs.fobshanghai.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-04-08T14:10:09Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bbs.fobshanghai.com"}, {"id": "d80e64e1320c", "title": "跨境知道卖家导航-集合跨境卖家出海所需的一切资源门户网站_跨境知道", "url": "https://tools.ikjzd.com/", "description": "来自 tools.ikjzd.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-04-09T22:37:59Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "tools.ikjzd.com"}, {"id": "2edf8eab04cb", "title": "UPC在线生成|海外仓,海外仓美国,一件代发,退货换标", "url": "https://www.haiwaicang.com/upc.html", "description": "来自 www.haiwaicang.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-04-16T15:04:57Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.haiwaicang.com"}, {"id": "77d8504243c6", "title": "专利查询", "url": "https://lin.wxjyxwt.cn/gj/?unit=guojia&keyword=gjzlgbggcx&e_creative=81202909945&e_adposition=cl1&e_keywordid=665384644777&e_keywordid2=606130198995&bd_vid=11217509308245480358", "description": "来自 lin.wxjyxwt.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-04-19T10:57:55Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "lin.wxjyxwt.cn"}, {"id": "10b0a81bbdcd", "title": "Best Product Research Tool", "url": "https://ixspy.com/data#/dashboard", "description": "来自 ixspy.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2023-10-04T13:46:58Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "ixspy.com"}, {"id": "87319eb845a6", "title": "世界各地时间换算-时间换算工具-AMZ123跨境导航", "url": "https://www.amz123.com/tools-timeconversion", "description": "来自 www.amz123.com 的书签", "category": "Tiktok", "tags": ["工具"], "date_added": "2024-01-02T10:10:16Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.amz123.com"}, {"id": "7d7fb42b9fee", "title": "Seller Center", "url": "https://gsp.aliexpress.com/", "description": "来自 gsp.aliexpress.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2022-02-28T10:05:05Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "gsp.aliexpress.com"}, {"id": "6b2b187ae084", "title": "免费UPC在线生成工具-AMZ123跨境导航", "url": "https://www.amz123.com/tools-upc", "description": "来自 www.amz123.com 的书签", "category": "Tiktok", "tags": ["工具"], "date_added": "2024-04-22T11:13:41Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.amz123.com"}, {"id": "185c8c05eb69", "title": "Generador de DNI (NIF, NIE, CIF) - generador-de-dni.com", "url": "https://www.generador-de-dni.com/validador-de-dni", "description": "来自 www.generador-de-dni.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-04-30T17:37:28Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.generador-de-dni.com"}, {"id": "61866b8134dc", "title": "您的广告 ads - Cdiscount", "url": "https://marketplace.cdiscount.com/zh/service/premium-ads/", "description": "来自 marketplace.cdiscount.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-05-09T16:45:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "marketplace.cdiscount.com"}, {"id": "4b96b2d94055", "title": "跨境资料库-跨境电商资料大全-雨果网", "url": "https://www.cifnews.com/links/data", "description": "来自 www.cifnews.com 的书签", "category": "Tiktok", "tags": ["电商"], "date_added": "2024-05-14T16:16:50Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.cifnews.com"}, {"id": "d2fa0139d18a", "title": "邮箱穷举猜测工具-福步外贸论坛", "url": "https://bbs.fobshanghai.com/emailformat/fuju.php", "description": "来自 bbs.fobshanghai.com 的书签", "category": "Tiktok", "tags": ["工具"], "date_added": "2024-05-15T18:07:07Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bbs.fobshanghai.com"}, {"id": "98b021d75d52", "title": "KC_202403221504357350.pdf - 常规产品认证/KC, KTC - ID: 109955603 - Industry Support Siemens", "url": "https://support.industry.siemens.com/cs/document/109955603/%E5%B8%B8%E8%A7%84%E4%BA%A7%E5%93%81%E8%AE%A4%E8%AF%81-kc-ktc?dti=0&lc=zh-CN", "description": "来自 support.industry.siemens.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-05-17T11:09:06Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "support.industry.siemens.com"}, {"id": "e2368e71c78b", "title": "‌‬​‌‬‌​⁣‍⁣​​‬﻿⁣​​‬⁢⁣﻿‌⁣﻿⁡‌​‬⁢﻿⁡‬⁡﻿⁤​‬‍‍⁢⁢﻿‬​‍﻿⁢‌‬‍【非服全托管模式】供应商上架推款培训 - Feishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/G1SodEkUpo7E16xZmFHcCB6jnbe", "description": "来自 bytedance.sg.larkoffice.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-19T14:28:57Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bytedance.sg.larkoffice.com"}, {"id": "4f6ce910a7a3", "title": "Certification (FCC, CE, RoHS, etc): | Holybro Docs", "url": "https://docs.holybro.com/company/certification-fcc-ce-rohs-etc", "description": "来自 docs.holybro.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-21T11:46:16Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "docs.holybro.com"}, {"id": "09d41d2e3106", "title": "Dragino Download Server ./downloads/HE/certificate/CE NB Certificate Report/", "url": "https://www.dragino.com/downloads/index.php?dir=HE/certificate/CE%20NB%20Certificate%20Report/", "description": "来自 www.dragino.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-24T09:52:56Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.dragino.com"}, {"id": "3527a60beb36", "title": "全部商品页", "url": "https://shop534c776216a47.1688.com/page/offerlist.htm?spm=0.0.wp_pc_common_topnav_38229151.0", "description": "来自 shop534c776216a47.1688.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-26T09:58:32Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "shop534c776216a47.1688.com"}, {"id": "966ec42e7834", "title": "德国EPR", "url": "https://oeffentliche-register.verpackungsregister.org/DeclarationOfCompleteness", "description": "来自 oeffentliche-register.verpackungsregister.org 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-26T17:08:21Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "oeffentliche-register.verpackungsregister.org"}, {"id": "cd7d7f1b732f", "title": "紧要通知！卖家怎么查询德国EPR号是否注册成功？-雨果网", "url": "https://www.cifnews.com/article/114800", "description": "来自 www.cifnews.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-26T17:11:02Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.cifnews.com"}, {"id": "2a9a077057d8", "title": "EPR废弃电气设备登记基金会", "url": "https://www.ear-system.de/ear-verzeichnis/hersteller.jsf#no-back", "description": "来自 www.ear-system.de 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-26T17:31:37Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.ear-system.de"}, {"id": "1f24f076181a", "title": "Latest FCC ID - Google 表格", "url": "https://docs.google.com/spreadsheets/d/1hGKSUkayxOMrGbHA4JJTbVXBbA8e4fmhWJ_4nzEZ81c/edit?pli=1&gid=0#gid=0", "description": "来自 docs.google.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-26T18:05:21Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "docs.google.com"}, {"id": "68675787c4f9", "title": "Shenzhen Baseus Technology Baseus Security P1 Indoor Camera 3K S0TV01 FCC ID 2A482-S0TV01", "url": "https://fccid.io/2A482-S0TV01", "description": "来自 fccid.io 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-26T18:09:01Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "fccid.io"}, {"id": "475c2a08ae2d", "title": "Docs", "url": "https://bytedance.sg.larkoffice.com/docx/ICuFd7pcooNEb7xHpeYl5unXgze", "description": "来自 bytedance.sg.larkoffice.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-27T16:50:15Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bytedance.sg.larkoffice.com"}, {"id": "4c544db49962", "title": "‍‬​﻿⁣⁤⁡⁡⁡﻿​​⁡⁡​⁤﻿⁢​⁣⁣‌‌⁤​⁡​​​⁣⁤​⁤‌﻿‍⁢﻿​⁢​⁡​​​﻿​‍﻿手机数码【分流指引】-各类目买手对接群/订单运营群 - Feishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/RrR2dyc4loeUsFxfGqLcJl5Hnwb", "description": "来自 bytedance.sg.larkoffice.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-27T18:12:05Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bytedance.sg.larkoffice.com"}, {"id": "9aea53f6040b", "title": "关于加强商品资质管控的通告 - TikTok Shop全托管卖家中心学习平台", "url": "https://learning-center.fanczs.com/support/content/138952?graphId=654&hideFooter=1&hideUnify=1&mappingType=2&pageId=441&spaceId=235×tamp=1702292818830", "description": "来自 learning-center.fanczs.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-06-28T15:11:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "learning-center.fanczs.com"}, {"id": "d179b46ca473", "title": "pss-system.cponline.cnipa.gov.cn/conventionalSearch", "url": "https://pss-system.cponline.cnipa.gov.cn/conventionalSearch", "description": "来自 pss-system.cponline.cnipa.gov.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-06-28T16:43:35Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "pss-system.cponline.cnipa.gov.cn"}, {"id": "67a853ed9a7f", "title": "电子电器类商品合规规范 - TikTok Shop全托管卖家中心学习平台", "url": "https://learning-center.fanczs.com/support/content/140923?graphId=654&hideFooter=1&hideUnify=1&mappingType=2&pageId=441&spaceId=235×tamp=1719559396800", "description": "来自 learning-center.fanczs.com 的书签", "category": "Tiktok", "tags": ["Tiktok"], "date_added": "2024-06-28T16:49:12Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "learning-center.fanczs.com"}, {"id": "02d21131923d", "title": "免费在线下载 YouTube 视频 - Y2Down.app", "url": "https://y2down.app/zh/youtube-video-downloader-zh/#url=https://www.youtube.com/watch?v=9zZwM9z0934", "description": "来自 y2down.app 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-02T10:11:19Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "y2down.app"}, {"id": "bdc9f7d2a634", "title": "CE certificates for Electronic Control Unit (KFG)", "url": "https://flex-homologation.com/BMW", "description": "来自 flex-homologation.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-02T14:10:03Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "flex-homologation.com"}, {"id": "3bd8e86faef4", "title": "FCC ID Search", "url": "https://fccid.io/", "description": "来自 fccid.io 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-03T11:07:52Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "fccid.io"}, {"id": "d5135dfc871f", "title": "‍​⁣​‍⁡​‍⁣⁡⁤⁤⁣​​⁡⁢‌⁡⁣⁢⁡​﻿⁣​⁣⁢⁤​​⁢​⁢​‍​⁣‍﻿​⁢⁡⁣‬‬⁡⁡﻿​第6期 商品资质提交常见问题 - Feishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/BHP7dDveVocWV6xNfysctiuFnah", "description": "来自 bytedance.sg.larkoffice.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-03T13:51:22Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bytedance.sg.larkoffice.com"}, {"id": "7efdce0ada98", "title": "‌‍⁤⁢‬‬⁢⁤⁢‬⁡⁢⁡⁣⁤‌⁡​​⁣⁣​​⁣⁣⁣​⁢‍⁢​​⁢⁣​‌‍⁡⁡‌﻿﻿​⁣‌‬‌‬​﻿如何使用嘀嗒狗数据判断某个品能不能做 - Feishu Docs", "url": "https://didagogo.feishu.cn/docx/JyGndNRVioAQ4UxGb7pcF9PUnQb", "description": "来自 didagogo.feishu.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-11T18:04:25Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "didagogo.feishu.cn"}, {"id": "c52bcf5d8066", "title": "‍⁣​​‬﻿⁢⁡⁡‍⁤​‬‍​⁤﻿⁡⁢⁡⁡⁣﻿​⁤​​⁡⁡﻿⁢﻿⁣​​⁡‌​‌⁤⁣‌⁢⁤‍﻿⁢‍​‍全托管商家-化妆品资质标签要求 - <PERSON>ishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/Usl9dSPlyom5OpxanzuchjMBnlf", "description": "来自 bytedance.sg.larkoffice.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-07-13T17:33:11Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "bytedance.sg.larkoffice.com"}, {"id": "138daeb3aee8", "title": "发现 - 雨果问答-跨境电商权威知识问答平台", "url": "https://www.cifnews.com/ask", "description": "来自 www.cifnews.com 的书签", "category": "Tiktok", "tags": ["电商"], "date_added": "2024-07-22T11:35:46Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.cifnews.com"}, {"id": "b7ba7ecb3c1b", "title": "美国地址生成，美国人虚构信息生成-世界各国虚拟身份信息、地址、信用卡生成", "url": "https://www.haoweichi.com/", "description": "来自 www.haoweichi.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-09-02T11:50:30Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.haoweichi.com"}, {"id": "7a60bb223f8b", "title": "泰国海外产品库", "url": "https://www.kdocs.cn/l/cdfYOfJSg6UI", "description": "来自 www.kdocs.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-11-19T18:14:56Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.kdocs.cn"}, {"id": "60f31d8af745", "title": "售后联系", "url": "https://www.kdocs.cn/l/cg1W82fld68a", "description": "来自 www.kdocs.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-11-19T18:15:04Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.kdocs.cn"}, {"id": "9aa0758a4ed8", "title": "金山文档 | WPS云文档", "url": "https://www.kdocs.cn/team/2344622591?guideShowTag=true", "description": "来自 www.kdocs.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2024-11-19T18:15:10Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.kdocs.cn"}, {"id": "2c61b3a698f6", "title": "美国地址生成器 - 随机生成美国地址和个人身份信息", "url": "https://www.usaddrgen.com/", "description": "来自 www.usaddrgen.com 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-06-03T16:20:59Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.usaddrgen.com"}, {"id": "f7c97c769665", "title": "全国企业信息查询系统", "url": "https://test1.javaw.icu/", "description": "来自 test1.javaw.icu 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-06-19T21:14:42Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "test1.javaw.icu"}, {"id": "968f24661098", "title": "JDL国内库存表格", "url": "https://www.kdocs.cn/l/caSWWxarIWrZ", "description": "来自 www.kdocs.cn 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-07-10T17:40:46Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "www.kdocs.cn"}, {"id": "ac7171fc755f", "title": "DIP", "url": "https://search.ipthailand.go.th/index2?q=JTdCJTIycSUyMiUzQSUyMmx1bmElMjIlMkMlMjJpbmRleCUyMiUzQSUyMmRpcF9zZWFyY2hfM190bSUyMiUyQyUyMmRpc3BsYXklMjIlM0ElMjJkaXBfc2VhcmNoXyolMjIlMkMlMjJpbmRleF9jcmVhdGUlMjIlM0ElMjJkaXBfc2VhcmNoXzNfdG0lMjIlMkMlMjJpbiUyMiUzQTMlMkMlMjJvcmRlciUyMiUzQSUyMl9zY29yZSUyQ2Rlc2MlMjIlMkMlMjJ0eXBlJTIyJTNBJTIyc2VhcmNoX2FsbF9zY3MlMjIlMkMlMjJ0eXBlX25hbWUlMjIlM0ElMjIlRTAlQjklODAlRTAlQjglODQlRTAlQjglQTMlRTAlQjglQjclRTAlQjklODglRTAlQjglQUQlRTAlQjglODclRTAlQjglQUIlRTAlQjglQTElRTAlQjglQjIlRTAlQjglQTIlRTAlQjglODElRTAlQjglQjIlRTAlQjglQTMlRTAlQjglODQlRTAlQjklODklRTAlQjglQjIlMjIlMkMlMjJ0YWJfaW5kZXglMjIlM0ElMjJkaXBfc2VhcmNoXzNfdG0lMjIlMkMlMjJkZl9pbmRleCUyMiUzQSUyMmRpcF9zZWFyY2hfM190bSUyMiUyQyUyMmJ1Y2tldHMlMjIlM0ElNUIlN0IlMjJkb2NfY291bnQlMjIlM0E0NTMlMkMlMjJrZXklMjIlM0ElMjJkaXBfc2VhcmNoXzNfdG0lMjIlN0QlNUQlN0Q%3D", "description": "来自 search.ipthailand.go.th 的书签", "category": "Tiktok", "tags": [], "date_added": "2025-07-21T13:43:08Z", "folder_path": ["书签栏", "电商", "Tiktok"], "domain": "search.ipthailand.go.th"}], "物流": [{"id": "609735c7f9d5", "title": "ALL-IN-ONE PACKAGE TRACKING | 17TRACK", "url": "https://www.17track.net/en", "description": "来自 www.17track.net 的书签", "category": "物流", "tags": [], "date_added": "2020-05-07T09:16:03Z", "folder_path": ["书签栏", "物流"], "domain": "www.17track.net"}, {"id": "7cdfdf67b9d2", "title": "4PX", "url": "http://track.4px.com/query/?locale=en_US", "description": "来自 track.4px.com 的书签", "category": "物流", "tags": [], "date_added": "2020-05-06T10:44:44Z", "folder_path": ["书签栏", "物流"], "domain": "track.4px.com"}, {"id": "3397031149ed", "title": "顺友物流查询平台 | Sunyou Package Tracking", "url": "https://www.sypost.net/index.html", "description": "来自 www.sypost.net 的书签", "category": "物流", "tags": [], "date_added": "2020-05-11T11:02:33Z", "folder_path": ["书签栏", "物流"], "domain": "www.sypost.net"}, {"id": "5d22fc752af9", "title": "全球物流跟踪", "url": "https://global.cainiao.com/detail.htm?mailNoList", "description": "来自 global.cainiao.com 的书签", "category": "物流", "tags": [], "date_added": "2020-07-21T18:55:15Z", "folder_path": ["书签栏", "物流"], "domain": "global.cainiao.com"}, {"id": "e6909802e6ac", "title": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "url": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "description": "来自 oms.yunexpress.cn 的书签", "category": "物流", "tags": [], "date_added": "2021-09-29T15:14:59Z", "folder_path": ["书签栏", "物流"], "domain": "oms.yunexpress.cn"}, {"id": "f1cc5b55760f", "title": "燕文物流", "url": "https://portal.yw56.com.cn/systemManagement/userMaintenance", "description": "来自 portal.yw56.com.cn 的书签", "category": "物流", "tags": [], "date_added": "2021-10-09T16:28:07Z", "folder_path": ["书签栏", "物流"], "domain": "portal.yw56.com.cn"}, {"id": "d77b8d447e8a", "title": "Tracking Results | Yuntrack - YunExpress", "url": "http://new.yuntrack.com/list", "description": "来自 new.yuntrack.com 的书签", "category": "物流", "tags": [], "date_added": "2021-07-02T14:14:50Z", "folder_path": ["书签栏", "物流"], "domain": "new.yuntrack.com"}, {"id": "eaa39204574f", "title": "船讯网--船舶动态、船舶档案、AIS船位、货物跟踪、租船、OP、航运大数据", "url": "https://www.shipxy.com/", "description": "来自 www.shipxy.com 的书签", "category": "物流", "tags": ["Ai"], "date_added": "2022-08-13T14:02:56Z", "folder_path": ["书签栏", "物流"], "domain": "www.shipxy.com"}, {"id": "af502dde1782", "title": "海外刷，外贸电商互动平台的领跑者,提升流量,提升信誉,让电商钱途无限!", "url": "http://**************:8080/admin/index", "description": "来自 **************:8080 的书签", "category": "物流", "tags": ["电商"], "date_added": "2023-02-28T14:21:40Z", "folder_path": ["书签栏", "物流"], "domain": "**************:8080"}, {"id": "7a6e6d8625ba", "title": "注册帐号 | Ozon Help", "url": "https://docs.ozon.ru/global/zh/launch/steps/", "description": "来自 docs.ozon.ru 的书签", "category": "物流", "tags": [], "date_added": "2023-02-28T15:15:06Z", "folder_path": ["书签栏", "物流"], "domain": "docs.ozon.ru"}, {"id": "e1214028cc26", "title": "仓储管理系统", "url": "http://cititrans.yunwms.com/", "description": "来自 cititrans.yunwms.com 的书签", "category": "物流", "tags": [], "date_added": "2023-09-27T09:23:12Z", "folder_path": ["书签栏", "物流"], "domain": "cititrans.yunwms.com"}, {"id": "fd1cc02e6f88", "title": "Fake Name Generator | FauxID.com", "url": "https://fauxid.com/fake-name-generator/south-korea?gender=male", "description": "来自 fauxid.com 的书签", "category": "物流", "tags": [], "date_added": "2023-11-14T14:02:52Z", "folder_path": ["书签栏", "物流"], "domain": "fauxid.com"}, {"id": "b8232aff2c14", "title": "Busca CEP --- 邮政编码搜索", "url": "https://buscacepinter.correios.com.br/app/endereco/index.php?spm=a2d0d.11623551.0.0.5043253b5Uh9G0", "description": "来自 buscacepinter.correios.com.br 的书签", "category": "物流", "tags": [], "date_added": "2023-11-24T14:53:03Z", "folder_path": ["书签栏", "物流"], "domain": "buscacepinter.correios.com.br"}, {"id": "df867baa2ba8", "title": "艾姆勒海外仓-订单管理系统", "url": "http://oms.imlb2c.com/", "description": "来自 oms.imlb2c.com 的书签", "category": "物流", "tags": [], "date_added": "2024-03-01T16:51:56Z", "folder_path": ["书签栏", "物流"], "domain": "oms.imlb2c.com"}, {"id": "ea03492172f2", "title": "首页 - 日升辉", "url": "http://rsh.itdida.com/itdida-flash/desktop/client-portal", "description": "来自 rsh.itdida.com 的书签", "category": "物流", "tags": [], "date_added": "2024-03-29T13:54:16Z", "folder_path": ["书签栏", "物流"], "domain": "rsh.itdida.com"}, {"id": "252830729e88", "title": "全球物流查询平台 | 17TRACK", "url": "https://www.17track.net/zh-cn", "description": "来自 www.17track.net 的书签", "category": "物流", "tags": [], "date_added": "2023-10-23T14:59:47Z", "folder_path": ["书签栏", "物流"], "domain": "www.17track.net"}, {"id": "2a84ef5c5b8f", "title": "首页 - OMS", "url": "https://oms-cntodd.xlwms.com/", "description": "来自 oms-cntodd.xlwms.com 的书签", "category": "物流", "tags": [], "date_added": "2024-09-27T09:07:37Z", "folder_path": ["书签栏", "物流"], "domain": "oms-cntodd.xlwms.com"}, {"id": "1e7d2725fce9", "title": "鹏城物流系统", "url": "http://pc.kingtrans.net/nclient/Logon?action=initMenu", "description": "来自 pc.kingtrans.net 的书签", "category": "物流", "tags": [], "date_added": "2024-08-22T10:57:22Z", "folder_path": ["书签栏", "物流"], "domain": "pc.kingtrans.net"}, {"id": "7545cde6f171", "title": "仓储管理系统", "url": "https://cititrans.yunwms.com/", "description": "来自 cititrans.yunwms.com 的书签", "category": "物流", "tags": [], "date_added": "2025-07-24T11:41:50Z", "folder_path": ["书签栏", "物流"], "domain": "cititrans.yunwms.com"}], "android": [{"id": "5d5f2cf8f16b", "title": "FreeBuf.COM | 关注黑客与极客", "url": "http://www.freebuf.com/", "description": "来自 www.freebuf.com 的书签", "category": "android", "tags": [], "date_added": "2015-12-25T16:27:25Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.freebuf.com"}, {"id": "84fc475246cd", "title": "Java 教程 | 菜鸟教程", "url": "http://www.runoob.com/java/java-tutorial.html", "description": "来自 www.runoob.com 的书签", "category": "android", "tags": ["教程"], "date_added": "2017-11-16T22:08:45Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.runoob.com"}, {"id": "27ba15df5243", "title": "【动态ab分区】动态分区解锁system而能与lsp或edxp框架共存（小白保姆级别教程） 来自 Kpfc白中白 - 酷安", "url": "https://www.coolapk.com/feed/27017797?shareKey=MzUzYzI1NDkwYTMzNjBmMDVhZTg~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": ["教程"], "date_added": "2021-07-16T01:48:18Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "6f74dae97dd4", "title": "termux配置DNA小白保姆级别安装教程（内含出错情况的解决方案） 来自 Kpfc白中白 - 酷安", "url": "https://www.coolapk.com/feed/26779553?shareKey=MWRiNzY4MmJiN2QzNjBmMDYyZDI~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": ["教程"], "date_added": "2021-07-16T01:48:34Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "99239c8c6a59", "title": "Android verified boot 2.0 vbmeta 数据结构解析 - 简书", "url": "https://www.jianshu.com/p/a2542426bdde", "description": "来自 www.jianshu.com 的书签", "category": "android", "tags": [], "date_added": "2021-07-20T01:34:03Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.jianshu.com"}, {"id": "f5902b12634f", "title": "出厂安卓11的机型之VAB架构的详细分析 来自 Rannki - 酷安", "url": "https://www.coolapk.com/feed/27525474?shareKey=ODE0ODY2MGNmY2NkNjBmNjI3M2Y~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": [], "date_added": "2021-07-20T16:40:34Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "51fa66658a21", "title": "出厂安卓11机型解锁system分区教程 来自 Rannki - 酷安", "url": "https://www.coolapk.com/feed/26090868?shareKey=MDM1NzZjZjFkMWVlNjEwNmNkOGQ~&shareUid=431416&shareFrom=com.coolapk.market_11.2.8", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": ["教程"], "date_added": "2021-08-02T00:39:45Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "cad769a08942", "title": "来自 响當當 - 酷安", "url": "https://www.coolapk.com/feed/27072044?shareKey=YWM1OTM5MDUwYjg0NjEwN2IxZDc~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": [], "date_added": "2021-08-02T16:50:53Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "d31e3bd9a7c0", "title": "菜鸡互啄--D.N.A解密官方包图文经验参考 来自 かかか - 酷安", "url": "https://www.coolapk.com/feed/28976999?shareKey=ZmVkZTA2YTNmNGEzNjExODA1MDI~&shareUid=431416&shareFrom=com.coolapk.market_11.3", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": [], "date_added": "2021-08-15T02:02:00Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "5bf800883a94", "title": "SuperR Kitchen | XDA Forums", "url": "https://forum.xda-developers.com/f/superr-kitchen.6337/", "description": "来自 forum.xda-developers.com 的书签", "category": "android", "tags": [], "date_added": "2021-08-16T17:19:25Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "forum.xda-developers.com"}, {"id": "8d6775a3220f", "title": "V-AB机型无视版本和系统更新限制，手动命令刷任意版本的官方ROM 来自 残芯此生不换_TWRP - 酷安", "url": "https://www.coolapk.com/feed/29368917?shareKey=ZWFmZWEyN2ZmYTIxNjEyMGE0MmE~&shareUid=431416&shareFrom=com.coolapk.market_11.3.1-beta1", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": [], "date_added": "2021-08-21T15:14:14Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "6b314010816e", "title": "系统更新偷渡器修改教程 来自 柚稚的孩纸 - 酷安", "url": "https://www.coolapk.com/feed/28372396?shareKey=Y2FhNTZlMmVkNDBlNjEyMGFmYmY~&shareUid=431416&shareFrom=com.coolapk.market_11.3.1-beta1", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": ["教程"], "date_added": "2021-08-21T15:48:57Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "2a4184f0e99f", "title": "Fastboot Enhance —— 适合人类使用的Fastboot + Payload.bin 解包工具箱 - AKR社区", "url": "https://www.akr-developers.com/d/506", "description": "来自 www.akr-developers.com 的书签", "category": "android", "tags": ["工具"], "date_added": "2021-08-21T17:50:39Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.akr-developers.com"}, {"id": "73decf2f5182", "title": "开发 Android 设备  |  Android 开源项目  |  Android Open Source Project", "url": "https://source.android.google.cn/devices", "description": "来自 source.android.google.cn 的书签", "category": "android", "tags": ["开源"], "date_added": "2021-08-28T01:12:52Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "source.android.google.cn"}, {"id": "1eda681ec173", "title": "一起来编译！面对小白的ROM编译教程（完全体）（可能是唯一有用的ROM编译教程（？）） 来自 SakuraiKR - 酷安", "url": "https://www.coolapk.com/feed/24966438?shareKey=MWM3NzNhMjUyNDcyNjE2NzA5ZDA~", "description": "来自 www.coolapk.com 的书签", "category": "android", "tags": ["教程", "Ai"], "date_added": "2021-10-14T00:48:21Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.coolapk.com"}, {"id": "4a0f34265e87", "title": "给红米Note 4X编译LineageOS 14.1刷机包过程【详细】_skyshell的专栏-CSDN博客", "url": "https://blog.csdn.net/fftt516/article/details/78160488/", "description": "来自 blog.csdn.net 的书签", "category": "android", "tags": [], "date_added": "2021-10-15T01:17:30Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "blog.csdn.net"}, {"id": "a7ba94826f4e", "title": "自己动手编译Android(LineageOS)源码 - 刷机党资源网", "url": "https://www.irom.net/post/42.html", "description": "来自 www.irom.net 的书签", "category": "android", "tags": [], "date_added": "2021-10-15T01:41:24Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.irom.net"}, {"id": "15dddf6fd823", "title": "cm13编译中的local manifest写法_高飞的专栏-CSDN博客", "url": "https://blog.csdn.net/feiniao8651/article/details/70162840", "description": "来自 blog.csdn.net 的书签", "category": "android", "tags": [], "date_added": "2021-10-15T01:41:33Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "blog.csdn.net"}, {"id": "a13eab9da34c", "title": "如何从Android固件文件中提取设备树文件 - 简书", "url": "https://www.jianshu.com/p/ceb407ffe9e5", "description": "来自 www.jianshu.com 的书签", "category": "android", "tags": [], "date_added": "2021-10-15T02:40:09Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "www.jianshu.com"}, {"id": "ba1f9a45f804", "title": "[TOOL] Android Image Kitchen - Unpack/Repack Kernel Ramdisk [Win/Android/Linux/Mac] | XDA Forums", "url": "https://forum.xda-developers.com/t/tool-android-image-kitchen-unpack-repack-kernel-ramdisk-win-android-linux-mac.2073775/", "description": "来自 forum.xda-developers.com 的书签", "category": "android", "tags": [], "date_added": "2021-08-26T19:13:24Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "forum.xda-developers.com"}, {"id": "2350d1bb7c77", "title": "XiaomiROM.com - 小米 ROM 线刷包, 卡刷包的最新及历史版本下载", "url": "https://xiaomirom.com/", "description": "来自 xiaomirom.com 的书签", "category": "android", "tags": [], "date_added": "2021-09-05T04:20:43Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "xiaomirom.com"}, {"id": "e61f51d222c6", "title": "Treble-Enabled Device Development A/AB ROMS | XDA Forums", "url": "https://forum.xda-developers.com/f/treble-enabled-device-development-a-ab-roms.7260/", "description": "来自 forum.xda-developers.com 的书签", "category": "android", "tags": [], "date_added": "2021-10-13T01:14:31Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "forum.xda-developers.com"}, {"id": "38bfaf1d7413", "title": "UnlockTool 2022.06.24.1 Update Link Setup Free Download", "url": "https://gsmxt.com/unlocktool-update-link-setup-free-download/", "description": "来自 gsmxt.com 的书签", "category": "android", "tags": [], "date_added": "2022-06-29T02:41:28Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "gsmxt.com"}, {"id": "89f132ad6fc4", "title": "Mediatek Metamode Native protocol Source Code - GSM Alphabet | Buy Source", "url": "https://alephgsm.com/2021/12/19/mediatek-metamode-native-protocol-source-code/", "description": "来自 alephgsm.com 的书签", "category": "android", "tags": [], "date_added": "2022-07-03T17:21:38Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "alephgsm.com"}, {"id": "560e8d1b06d6", "title": "Win11安卓子系统 WSA 安装教程，Win11安装安卓应用 亲测有效！ – 优质盒子", "url": "https://uzbox.com/tech/wsa-pacman.html", "description": "来自 uzbox.com 的书签", "category": "android", "tags": ["教程"], "date_added": "2023-02-22T16:45:09Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "uzbox.com"}, {"id": "97c8d8164b81", "title": "在非gki内核中添加KernelSU支持_如何为非 gki 内核集成 kernelsu-CSDN博客", "url": "https://blog.csdn.net/qq_43283565/article/details/137374337", "description": "来自 blog.csdn.net 的书签", "category": "android", "tags": [], "date_added": "2024-10-02T23:40:17Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "blog.csdn.net"}, {"id": "a87e434e6ffa", "title": "🏹 在小米平板 5 上安装 Arch Linux - 風雪城", "url": "https://blog.chyk.ink/2024/06/22/mipad5-archlinux/", "description": "来自 blog.chyk.ink 的书签", "category": "android", "tags": [], "date_added": "2024-11-17T03:47:41Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "blog.chyk.ink"}, {"id": "71080de9d9e9", "title": "timoxa0/Switch2Linux-Nabu: Moved to https://g.tx0.su/timoxa0/Switch2Linux-Nabu", "url": "https://github.com/timoxa0/Switch2Linux-Nabu?tab=readme-ov-file", "description": "来自 github.com 的书签", "category": "android", "tags": ["GitHub"], "date_added": "2024-11-27T19:17:05Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "github.com"}, {"id": "f582dd472421", "title": "构建用于 Waydroid 的 LineageOS20 系统 - InSnh-Gd", "url": "https://xlog.insnhgd.com/2?locale=zh", "description": "来自 xlog.insnhgd.com 的书签", "category": "android", "tags": [], "date_added": "2024-11-30T17:36:45Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "xlog.insnhgd.com"}, {"id": "97c942ec89cc", "title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/Simplified Chinese/selection-cn.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-<PERSON>d-5/blob/main/guide/Simplified%20Chinese/selection-cn.md", "description": "来自 github.com 的书签", "category": "android", "tags": ["GitHub", "Ai"], "date_added": "2025-01-09T16:14:28Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "github.com"}, {"id": "12de5400335c", "title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/Simplified Chinese/won-deployer-install-cn.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5/blob/main/guide/Simplified%20Chinese/won-deployer-install-cn.md", "description": "来自 github.com 的书签", "category": "android", "tags": ["GitHub", "Ai"], "date_added": "2025-01-09T16:52:32Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "github.com"}, {"id": "262e4205e3b7", "title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/English/installation-selection-en.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-<PERSON>-5/blob/main/guide/English/installation-selection-en.md", "description": "来自 github.com 的书签", "category": "android", "tags": ["GitHub", "Ai"], "date_added": "2025-02-27T14:52:38Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "github.com"}, {"id": "95b612e61e73", "title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/English/won-deployer-install-en.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-<PERSON>d-5/blob/main/guide/English/won-deployer-install-en.md", "description": "来自 github.com 的书签", "category": "android", "tags": ["GitHub", "Ai"], "date_added": "2025-02-27T18:24:48Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "github.com"}, {"id": "3b4bcf239119", "title": "timoxa0/Switch2Linux-Nabu: App for linux dualboot on Nabu (Xiaomi Pad 5) - timoxa0's Forgejo instance", "url": "https://g.tx0.su/timoxa0/Switch2Linux-Nabu", "description": "来自 g.tx0.su 的书签", "category": "android", "tags": [], "date_added": "2025-02-27T22:30:12Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "g.tx0.su"}, {"id": "fc3a377535fe", "title": "Download TWRP for cheeseburger_dumpling", "url": "https://dl.twrp.me/cheeseburger_dumpling/", "description": "来自 dl.twrp.me 的书签", "category": "android", "tags": [], "date_added": "2025-03-31T17:00:15Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "dl.twrp.me"}, {"id": "2061421d703a", "title": "ChimeProjects - Browse /Pixel Projects at SourceForge.net", "url": "https://sourceforge.net/projects/chimeprojects/files/Pixel%20Projects/", "description": "来自 sourceforge.net 的书签", "category": "android", "tags": [], "date_added": "2025-06-17T10:13:13Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "sourceforge.net"}, {"id": "08e645a4155e", "title": "Triple-Boot-on-Xiaomi-Pad-5/guide/Install-LINUX.md at main · Xyy155/Triple-Boot-on-Xiaomi-Pad-5", "url": "https://github.com/Xyy155/<PERSON>-Boot-on-Xiaomi-Pad-5/blob/main/guide/Install-LINUX.md", "description": "来自 github.com 的书签", "category": "android", "tags": ["GitHub", "Ai"], "date_added": "2025-07-06T23:12:30Z", "folder_path": ["书签栏", "Geek", "android"], "domain": "github.com"}], "设计": [{"id": "7d386cb7d687", "title": "Featured Wallpapers - Wallpaper Abyss - Page 28", "url": "http://wall.alphacoders.com/featured.php?page=28", "description": "来自 wall.alphacoders.com 的书签", "category": "设计", "tags": [], "date_added": "2016-02-13T18:48:44Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "wall.alphacoders.com"}, {"id": "3d8ac35d710b", "title": "Banner设计 - 优优教程网", "url": "https://uiiiuiii.com/inspirations/banner#inspiration-menu?tdsourcetag=s_pctim_aiomsg", "description": "来自 uiiiuiii.com 的书签", "category": "设计", "tags": ["教程"], "date_added": "2020-01-02T19:43:54Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "uiiiuiii.com"}, {"id": "e4a59d867457", "title": "网页设计常用色彩搭配表 - 配色表 | 小影的工具箱", "url": "http://tool.c7sky.com/webcolor/#character_6", "description": "来自 tool.c7sky.com 的书签", "category": "设计", "tags": ["工具"], "date_added": "2023-02-03T10:21:43Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "tool.c7sky.com"}, {"id": "0e430003a590", "title": "Photoshop-LookAE.com", "url": "https://www.lookae.com/qitarjcj/pszy/", "description": "来自 www.lookae.com 的书签", "category": "设计", "tags": [], "date_added": "2023-02-20T18:37:25Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "www.lookae.com"}, {"id": "20c547337c61", "title": "An Ultimate list of 500 AI tools", "url": "https://spectacular-party-fc2.notion.site/An-Ultimate-list-of-500-AI-tools-8f737bef33af49fc97336dc9c819c695", "description": "来自 spectacular-party-fc2.notion.site 的书签", "category": "设计", "tags": ["Ai"], "date_added": "2023-02-20T23:12:16Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "spectacular-party-fc2.notion.site"}, {"id": "42a62a2ffe44", "title": "免费正版高清图片素材库 超过2.7百万张优质图片和视频素材可供免费使用和下载 - Pixabay - Pixabay", "url": "https://pixabay.com/zh/", "description": "来自 pixabay.com 的书签", "category": "设计", "tags": [], "date_added": "2023-02-21T12:16:26Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "pixabay.com"}, {"id": "e5e016c59f6f", "title": "Mega Creator – Easy & Free Online Graphic Design Software", "url": "https://icons8.com/mega-creator/dashboard", "description": "来自 icons8.com 的书签", "category": "设计", "tags": [], "date_added": "2023-02-21T12:27:25Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "icons8.com"}, {"id": "4633e99a7363", "title": "100,000+张最精彩的“Sky”图片 · 100%免费下载 · Pexels素材图片", "url": "https://www.pexels.com/zh-cn/search/sky/", "description": "来自 www.pexels.com 的书签", "category": "设计", "tags": [], "date_added": "2023-08-01T16:25:40Z", "folder_path": ["书签栏", "Geek", "设计"], "domain": "www.pexels.com"}], "media": [{"id": "563937378fe1", "title": "Mp4电影_最新电影下载_最新高清MP4电影资源下载", "url": "https://www.domp4.cc/", "description": "来自 www.domp4.cc 的书签", "category": "media", "tags": [], "date_added": "2022-03-20T22:25:05Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "www.domp4.cc"}, {"id": "09a042476117", "title": "MyFreeMP3", "url": "https://tool.liumingye.cn/music/#/explore/artist", "description": "来自 tool.liumingye.cn 的书签", "category": "media", "tags": [], "date_added": "2023-01-17T15:58:04Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "tool.liumingye.cn"}, {"id": "afaee27f1aee", "title": "Xslist.org - 健康的宅男偶像专题网站", "url": "https://xslist.org/zh", "description": "来自 xslist.org 的书签", "category": "media", "tags": [], "date_added": "2023-02-11T02:51:54Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "xslist.org"}, {"id": "f1c62745462e", "title": "ASMR Online", "url": "https://www.asmr.one/works", "description": "来自 www.asmr.one 的书签", "category": "media", "tags": [], "date_added": "2023-02-12T16:22:04Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "www.asmr.one"}, {"id": "8f0dbb00b3e9", "title": "Torrent Kitty - Free Torrent To Magnet Link Conversion Service", "url": "https://www.torkitty.com/search/", "description": "来自 www.torkitty.com 的书签", "category": "media", "tags": [], "date_added": "2023-02-17T22:44:15Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "www.torkitty.com"}, {"id": "2851862ad0ff", "title": "死神 千年血战篇-诀别谭 第04集 - 在线播放 - AGE动漫", "url": "https://www.agedm.org/play/20230135/1/4", "description": "来自 www.agedm.org 的书签", "category": "media", "tags": [], "date_added": "2024-02-10T21:33:15Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "www.agedm.org"}, {"id": "7a43bbebf396", "title": "MissAV | Watch HD JAV Online | Free & High Quality AV", "url": "https://missav123.com/dm22/en", "description": "来自 missav123.com 的书签", "category": "media", "tags": [], "date_added": "2025-06-20T01:55:07Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "missav123.com"}, {"id": "62d1a963b327", "title": "domain-list-community/data/category-porn at master · v2ray/domain-list-community", "url": "https://github.com/v2ray/domain-list-community/blob/master/data/category-porn", "description": "来自 github.com 的书签", "category": "media", "tags": ["GitHub", "Ai"], "date_added": "2025-07-20T09:38:32Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "github.com"}, {"id": "4071b805a0ca", "title": "影视站合集 短剧在线集合 在线播放 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/181385", "description": "来自 linux.do 的书签", "category": "media", "tags": ["Linux"], "date_added": "2025-07-20T15:29:15Z", "folder_path": ["书签栏", "Geek", "media"], "domain": "linux.do"}], "tool": [{"id": "5dc6216ce048", "title": "原版软件", "url": "https://next.itellyou.cn/Original/Index", "description": "来自 next.itellyou.cn 的书签", "category": "tool", "tags": [], "date_added": "2021-06-21T00:43:36Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "next.itellyou.cn"}, {"id": "326c0da6211c", "title": "登录 - 中国电信网上营业厅·广东", "url": "https://gd.189.cn/common/login.htm?loginOldUri=/service/pay/", "description": "来自 gd.189.cn 的书签", "category": "tool", "tags": [], "date_added": "2023-02-19T00:07:24Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "gd.189.cn"}, {"id": "3f1a6d1c6f89", "title": "广东电信宽带测速平台", "url": "https://10000.gd.cn/#/speed", "description": "来自 10000.gd.cn 的书签", "category": "tool", "tags": [], "date_added": "2023-02-26T03:16:38Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "10000.gd.cn"}, {"id": "9ac2e036b983", "title": "Upload Image – remove.bg", "url": "https://www.remove.bg/upload?full_image_id=e0d038ce-15c2-4882-afab-f293f636db2b", "description": "来自 www.remove.bg 的书签", "category": "tool", "tags": [], "date_added": "2023-02-28T15:59:24Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.remove.bg"}, {"id": "e913d983e800", "title": "WPS - 搜索结果 - 果核剥壳", "url": "https://www.ghxi.com/?s=WPS", "description": "来自 www.ghxi.com 的书签", "category": "tool", "tags": [], "date_added": "2023-10-18T11:53:55Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.ghxi.com"}, {"id": "65750f0535e9", "title": "XIU2/TrackersListCollection", "url": "https://trackerslist.com/#/zh", "description": "来自 trackerslist.com 的书签", "category": "tool", "tags": [], "date_added": "2023-10-18T22:33:55Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "trackerslist.com"}, {"id": "f628dda0e88c", "title": "(2023.9.5)SHELL脚本：一键给PVE增加温度,cpu功耗频率,硬盘等信息-软路由,x86系统,openwrt(x86),Router OS 等-恩山无线论坛", "url": "https://www.right.com.cn/forum/thread-6754687-1-1.html", "description": "来自 www.right.com.cn 的书签", "category": "tool", "tags": [], "date_added": "2023-11-11T17:52:07Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.right.com.cn"}, {"id": "d545022d82eb", "title": "Windows10专业版免费永久激活（亲测可用） - One heart - 博客园", "url": "https://www.cnblogs.com/oneheart/p/17231732.html", "description": "来自 www.cnblogs.com 的书签", "category": "tool", "tags": [], "date_added": "2024-04-10T00:14:08Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.cnblogs.com"}, {"id": "591e9fe4f226", "title": "totoroterror/warp-cloner: Simple Python script that can clone Warp Plus (*******) keys and generate 12PB (or 24PB) keys.", "url": "https://github.com/totoroterror/warp-cloner/tree/main", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub"], "date_added": "2024-04-13T14:19:49Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "2de1c89dafc9", "title": "Anaconda创建环境、删除环境、激活环境、退出环境_conda 删除环境-CSDN博客", "url": "https://blog.csdn.net/H_O_W_E/article/details/77370456", "description": "来自 blog.csdn.net 的书签", "category": "tool", "tags": [], "date_added": "2024-04-13T14:19:56Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "blog.csdn.net"}, {"id": "5f6b4faa2d60", "title": "lmc999/auto-add-routes: China Route for VPN", "url": "https://github.com/lmc999/auto-add-routes", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub"], "date_added": "2024-04-13T14:54:12Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "97693d8ea2ec", "title": "WARPconfig-youtube不一样的强哥 - Replit", "url": "https://replit.com/@304070820/WARPconfig-youtubeB<PERSON>-<PERSON>-<PERSON>-<PERSON>-<PERSON>e", "description": "来自 replit.com 的书签", "category": "tool", "tags": [], "date_added": "2024-04-13T16:17:03Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "replit.com"}, {"id": "69cd5897d627", "title": "【攻破了】WARP免费VPN一键实现网络分流，消除网友痛点！超详细教学网络代理分流！Warp+帐号生成、配置。安卓手机和window warp网络代理分流（WARP第四期） - YouTube", "url": "https://www.youtube.com/watch?v=Ll3T0AWtPWE", "description": "来自 www.youtube.com 的书签", "category": "tool", "tags": [], "date_added": "2024-04-13T16:31:10Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.youtube.com"}, {"id": "c9c0e88721e7", "title": "3款来自于 github.com 的 Windows 激活工具_技术攀登者的技术博客_51CTO博客", "url": "https://blog.51cto.com/u_9843231/6149019", "description": "来自 blog.51cto.com 的书签", "category": "tool", "tags": ["工具", "<PERSON><PERSON><PERSON>"], "date_added": "2024-04-15T11:17:04Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "blog.51cto.com"}, {"id": "aee3d0a03d8f", "title": "massgravel/Microsoft-Activation-Scripts: A Windows and Office activator using HWID / Ohook / KMS38 / Online KMS activation methods, with a focus on open-source code and fewer antivirus detections.", "url": "https://github.com/massgravel/Microsoft-Activation-Scripts", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub"], "date_added": "2024-04-15T11:18:41Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "785b16c32e38", "title": "Genshin Impact | GI | Mods & Resources", "url": "https://gamebanana.com/games/8552", "description": "来自 gamebanana.com 的书签", "category": "tool", "tags": [], "date_added": "2024-05-01T17:10:53Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "gamebanana.com"}, {"id": "063273140b3a", "title": "[2024.04.15更新] VCMI 1.5.0多平台中文版-英雄无敌3-WoG中文站 - Powered by <PERSON><PERSON>!", "url": "https://www.h3wog.com/thread-74994-1-1.html", "description": "来自 www.h3wog.com 的书签", "category": "tool", "tags": [], "date_added": "2024-05-19T14:17:24Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.h3wog.com"}, {"id": "15959fce9604", "title": "跨境AI亚马逊指令模板集合（一） - 知无不言跨境电商社区", "url": "https://www.wearesellers.com/article/18025", "description": "来自 www.wearesellers.com 的书签", "category": "tool", "tags": ["电商", "Ai"], "date_added": "2024-05-21T18:27:21Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.wearesellers.com"}, {"id": "54e929af68d3", "title": "Dragino Download Server ./downloads/LGT_92/Certificate/Anatel/", "url": "https://www.dragino.com/downloads/index.php?dir=LGT_92/Certificate/Anatel/", "description": "来自 www.dragino.com 的书签", "category": "tool", "tags": [], "date_added": "2024-05-30T15:02:53Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.dragino.com"}, {"id": "eb50b8071032", "title": "ANATEL_8321_CERT_HOMOLOGACAO.pdf - 无线电设备型式认证证书, 无线电设备型式认证证书 - ID: 109747330 - Industry Support Siemens", "url": "https://support.industry.siemens.com/cs/document/109747330/%E6%97%A0%E7%BA%BF%E7%94%B5%E8%AE%BE%E5%A4%87%E5%9E%8B%E5%BC%8F%E8%AE%A4%E8%AF%81%E8%AF%81%E4%B9%A6-%E6%97%A0%E7%BA%BF%E7%94%B5%E8%AE%BE%E5%A4%87%E5%9E%8B%E5%BC%8F%E8%AE%A4%E8%AF%81%E8%AF%81%E4%B9%A6?dti=0&dl=zh&lc=en-BY", "description": "来自 support.industry.siemens.com 的书签", "category": "tool", "tags": [], "date_added": "2024-05-30T15:30:32Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "support.industry.siemens.com"}, {"id": "634b8f72f15a", "title": "Hikvision's FCC Supplier's Declaration of Conformity (SDOC) - Documents - Hikvision", "url": "https://www.hikvision.com/us-en/support/document-center/fcc-sdoc/", "description": "来自 www.hikvision.com 的书签", "category": "tool", "tags": [], "date_added": "2024-05-31T14:49:17Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.hikvision.com"}, {"id": "753ff367a3f9", "title": "HYPEROSPRO - Browse /WEEKLY at SourceForge.net", "url": "https://sourceforge.net/projects/hyperospro/files/WEEKLY/", "description": "来自 sourceforge.net 的书签", "category": "tool", "tags": [], "date_added": "2024-06-04T11:28:10Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "sourceforge.net"}, {"id": "3fffdd257795", "title": "<PERSON><PERSON>", "url": "https://serverapi.cery.cloud/#/knowledge", "description": "来自 serverapi.cery.cloud 的书签", "category": "tool", "tags": [], "date_added": "2024-06-12T09:28:24Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "serverapi.cery.cloud"}, {"id": "f7e0a62b8852", "title": "成就百科 - JX3BOX", "url": "https://www.jx3box.com/cj/view/5176", "description": "来自 www.jx3box.com 的书签", "category": "tool", "tags": [], "date_added": "2024-06-23T19:04:25Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.jx3box.com"}, {"id": "987a6f3eec9e", "title": "使用 Ventoy 引导的 WTG 制作（试验性） | <PERSON><PERSON><PERSON><PERSON>'s Blog", "url": "https://hui-shao.com/ventoy-wtg/", "description": "来自 hui-shao.com 的书签", "category": "tool", "tags": [], "date_added": "2024-07-23T20:37:47Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "hui-shao.com"}, {"id": "cc459840c109", "title": "Tailscale 的 DERP 中继服务搭建与配置 - 白日梦观察", "url": "https://blog.angustar.com/archives/Tailscale-DERP.html", "description": "来自 blog.angustar.com 的书签", "category": "tool", "tags": ["Ai"], "date_added": "2024-07-30T18:33:56Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "blog.angustar.com"}, {"id": "8de1d777cd53", "title": "CodeWithGPU | 能复现才是好算法", "url": "https://www.codewithgpu.com/image", "description": "来自 www.codewithgpu.com 的书签", "category": "tool", "tags": [], "date_added": "2024-07-31T17:15:47Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.codewithgpu.com"}, {"id": "3359dde385e2", "title": "创建无人值守、高度自定义的纯净 Windows 11/10 系统镜像！ – 零度解说", "url": "https://www.freedidi.com/13121.html", "description": "来自 www.freedidi.com 的书签", "category": "tool", "tags": [], "date_added": "2024-07-31T18:13:50Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.freedidi.com"}, {"id": "3a3a19d8aa46", "title": "My IP Address - BrowserLeaks", "url": "https://browserleaks.com/ip", "description": "来自 browserleaks.com 的书签", "category": "tool", "tags": [], "date_added": "2024-08-01T17:59:19Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "browserleaks.com"}, {"id": "c378a10872f7", "title": "GitHub - LSPosed/MagiskOnWSALocal: Integrate Magisk root and Google Apps into WSA (Windows Subsystem for Android)", "url": "https://github.com/LSPosed/MagiskOnWSALocal", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2024-08-07T18:31:04Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "b5c570a5ac24", "title": "Windows安装WSL2精简版教程_wsl2安装-CSDN博客", "url": "https://blog.csdn.net/u011436427/article/details/135673366", "description": "来自 blog.csdn.net 的书签", "category": "tool", "tags": ["教程"], "date_added": "2024-08-07T18:29:09Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "blog.csdn.net"}, {"id": "d28c2c9f3a59", "title": "WSL2 中访问宿主机 Windows 的代理 - ZingLix Blog", "url": "https://zinglix.xyz/2020/04/18/wsl2-proxy/", "description": "来自 zinglix.xyz 的书签", "category": "tool", "tags": [], "date_added": "2024-08-08T14:30:52Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "zinglix.xyz"}, {"id": "771ec2dcb43f", "title": "Windows Android 子系统 WSA 代理设置方法教程 - 秋风于渭水", "url": "https://www.tjsky.net/tutorial/391", "description": "来自 www.tjsky.net 的书签", "category": "tool", "tags": ["教程"], "date_added": "2024-08-08T18:07:53Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.tjsky.net"}, {"id": "fb8d8febc72a", "title": "『WSL』如何停止或重启 WSL | NX の 博客", "url": "https://nickxu.me/2022/03/17/%E3%80%8EWSL%E3%80%8F%E5%A6%82%E4%BD%95%E5%81%9C%E6%AD%A2%E6%88%96%E9%87%8D%E5%90%AF-WSL/", "description": "来自 nickxu.me 的书签", "category": "tool", "tags": [], "date_added": "2024-08-09T09:51:09Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "nickxu.me"}, {"id": "5e1bbdb6debe", "title": "将 AVIF 转换为 JPG", "url": "https://converter.11zon.com/zh-cn/avif-to-jpg/", "description": "来自 converter.11zon.com 的书签", "category": "tool", "tags": [], "date_added": "2024-03-11T16:39:51Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "converter.11zon.com"}, {"id": "ae690f05e308", "title": "<PERSON>", "url": "https://claude.ai/new", "description": "来自 claude.ai 的书签", "category": "tool", "tags": [], "date_added": "2024-08-12T10:17:39Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "claude.ai"}, {"id": "e1c1d823b924", "title": "My Numbers | NumberBarn", "url": "https://www.numberbarn.com/account/numbers", "description": "来自 www.numberbarn.com 的书签", "category": "tool", "tags": [], "date_added": "2024-08-12T13:34:05Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.numberbarn.com"}, {"id": "39ed456b1f8e", "title": "美国电话卡Ultra Mobile Paygo套餐（3刀神卡）购买、激活及充值教程-VPS大玩家", "url": "https://www.vpsdawanjia.com/4989.html#Numberbarn", "description": "来自 www.vpsdawanjia.com 的书签", "category": "tool", "tags": ["教程"], "date_added": "2024-08-13T08:30:40Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.vpsdawanjia.com"}, {"id": "f991851f12b2", "title": "minihub/LTSC-Add-MicrosoftStore: Add Windows Store to Windows 11 24H2 LTSC", "url": "https://github.com/minihub/LTSC-Add-MicrosoftStore", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub"], "date_added": "2024-10-19T03:02:00Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "880284f43adb", "title": "[021]windows 11 安装 conda - 少数派", "url": "https://sspai.com/post/75430", "description": "来自 sspai.com 的书签", "category": "tool", "tags": [], "date_added": "2024-10-19T18:23:11Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "sspai.com"}, {"id": "0a4a9ab805b8", "title": "Jupyter 多环境选择以及使用方法_jupyternotebook怎么选择环境-CSDN博客", "url": "https://blog.csdn.net/zj_xd/article/details/130513680", "description": "来自 blog.csdn.net 的书签", "category": "tool", "tags": [], "date_added": "2024-10-19T18:29:23Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "blog.csdn.net"}, {"id": "5b324cd1cc3f", "title": "丸子君的新基地尊享版教程（看完哦❤杂鱼~❤）", "url": "https://docs.qq.com/aio/DSGdQc3htbFJjSFdO?p=DXpTjzl2kZwBjN7jlRMkRJ", "description": "来自 docs.qq.com 的书签", "category": "tool", "tags": ["教程"], "date_added": "2024-10-22T00:56:42Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "docs.qq.com"}, {"id": "e518f642794a", "title": "Sunshine 基地版串流食用指南", "url": "https://docs.qq.com/aio/DSGdQc3htbFJjSFdO?p=YTpMj5JNNdB5hEKJhhqlSB", "description": "来自 docs.qq.com 的书签", "category": "tool", "tags": [], "date_added": "2024-10-22T01:06:06Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "docs.qq.com"}, {"id": "454ba0fcc714", "title": "天翼一号2021官改包的简单体验 来自 负平生 - 酷安", "url": "https://www.coolapk.com/feed/58340359", "description": "来自 www.coolapk.com 的书签", "category": "tool", "tags": [], "date_added": "2024-11-01T17:49:00Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.coolapk.com"}, {"id": "910ea54869d2", "title": "Windows ARM software | ‎Home", "url": "https://armrepo.ver.lt/", "description": "来自 armrepo.ver.lt 的书签", "category": "tool", "tags": [], "date_added": "2024-11-26T00:47:04Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "armrepo.ver.lt"}, {"id": "505944ae2ddd", "title": "Honkai Star Rail | HSR | Mods & Resources", "url": "https://gamebanana.com/games/18366", "description": "来自 gamebanana.com 的书签", "category": "tool", "tags": ["Ai"], "date_added": "2024-12-01T23:52:53Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "gamebanana.com"}, {"id": "ff829f760089", "title": "成功让 nvidia tesla 工作在 WDDM（打游戏）模式的方法_服务软件_什么值得买", "url": "https://post.smzdm.com/p/a90q6055/", "description": "来自 post.smzdm.com 的书签", "category": "tool", "tags": [], "date_added": "2024-12-02T22:11:51Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "post.smzdm.com"}, {"id": "88e8f2a65596", "title": "xubiaolin/docker-zerotier-planet: 一分钟私有部署zerotier-planet服务", "url": "https://github.com/xubiaolin/docker-zerotier-planet", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub"], "date_added": "2024-12-04T18:02:11Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "e73bc7accebd", "title": "zerotier开启问题-OPENWRT专版-恩山无线论坛", "url": "https://www.right.com.cn/forum/thread-306636-1-1.html", "description": "来自 www.right.com.cn 的书签", "category": "tool", "tags": [], "date_added": "2024-12-05T09:30:03Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.right.com.cn"}, {"id": "4297cee30df8", "title": "收集整理的2024最新的常用VPS脚本工具 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/165688", "description": "来自 linux.do 的书签", "category": "tool", "tags": ["Linux", "工具"], "date_added": "2024-12-05T16:33:31Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "linux.do"}, {"id": "cc9b480bc152", "title": "Proxmox VE 7 配置 nvidia vGPU (vgpu_unlock) · woniuzfb/iptv Wiki · GitHub", "url": "https://github.com/woniuzfb/iptv/wiki/Proxmox-VE-7-%E9%85%8D%E7%BD%AE-nvidia-vGPU-(vgpu_unlock)", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2024-12-08T00:11:02Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "498b710f55c6", "title": "PVE8 直通 debian12 | 电脑硬件学习笔记", "url": "https://skyao.io/learning-computer-hardware/graphics/p102/pve-debian12/", "description": "来自 skyao.io 的书签", "category": "tool", "tags": [], "date_added": "2024-12-08T02:05:33Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "skyao.io"}, {"id": "a4fcf3988a0c", "title": "nVidia PCI id database — envytools git documentation", "url": "https://envytools.readthedocs.io/en/latest/hw/pciid.html", "description": "来自 envytools.readthedocs.io 的书签", "category": "tool", "tags": [], "date_added": "2024-12-08T00:12:03Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "envytools.readthedocs.io"}, {"id": "692838366d20", "title": "把WPS/Excel里的单元格为图片url链接转换为图片显示_wps链接转图片-CSDN博客", "url": "https://blog.csdn.net/hong_rui/article/details/130641869", "description": "来自 blog.csdn.net 的书签", "category": "tool", "tags": [], "date_added": "2024-12-17T16:26:56Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "blog.csdn.net"}, {"id": "1fd2bb4d0d73", "title": "WildCard | 一分钟注册，轻松订阅海外软件服务", "url": "https://bewildcard.com/card", "description": "来自 bewildcard.com 的书签", "category": "tool", "tags": [], "date_added": "2025-01-11T10:31:22Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "bewildcard.com"}, {"id": "603dcda258fa", "title": "FLUX.1 AI绘画小助手", "url": "https://share.fastgpt.in/chat/share?shareId=zlbbyirumxf8j0rpazajy96s", "description": "来自 share.fastgpt.in 的书签", "category": "tool", "tags": ["Ai"], "date_added": "2025-01-16T08:51:50Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "share.fastgpt.in"}, {"id": "298f16d1251e", "title": "宝可梦星云", "url": "https://web2.52pokemon.cc/dashboard", "description": "来自 web2.52pokemon.cc 的书签", "category": "tool", "tags": [], "date_added": "2025-01-20T13:18:10Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "web2.52pokemon.cc"}, {"id": "129b15d9b46d", "title": "全自动永久免费获取机场节点/订阅之最新食用方法 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/96234", "description": "来自 linux.do 的书签", "category": "tool", "tags": ["Linux"], "date_added": "2025-01-19T08:30:52Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "linux.do"}, {"id": "a591b9896167", "title": "技术版块", "url": "https://www.nodeseek.com/categories/tech", "description": "来自 www.nodeseek.com 的书签", "category": "tool", "tags": [], "date_added": "2025-01-23T23:31:47Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.nodeseek.com"}, {"id": "ae4e61357b70", "title": "Win客户端剪印专业版VIP破解的小方法 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/388042", "description": "来自 linux.do 的书签", "category": "tool", "tags": ["Linux"], "date_added": "2025-01-24T20:00:26Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "linux.do"}, {"id": "f0eea28d8704", "title": "全自动获取免费机场节点/订阅方法分享之青龙面板实现全自动订阅方式 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/303327/3", "description": "来自 linux.do 的书签", "category": "tool", "tags": ["Linux"], "date_added": "2025-01-24T20:21:20Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "linux.do"}, {"id": "ac789baa267e", "title": "GPTs/tutorials/【终极指南】Cursor 从安装到高效使用的完整教程.md at main · ljiao189/GPTs", "url": "https://github.com/ljiao189/GPTs/blob/main/tutorials/%E3%80%90%E7%BB%88%E6%9E%81%E6%8C%87%E5%8D%97%E3%80%91Cursor%20%E4%BB%8E%E5%AE%89%E8%A3%85%E5%88%B0%E9%AB%98%E6%95%88%E4%BD%BF%E7%94%A8%E7%9A%84%E5%AE%8C%E6%95%B4%E6%95%99%E7%A8%8B.md", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub", "教程", "Ai"], "date_added": "2025-01-25T00:06:33Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "8cae58b303c6", "title": "xubiaolin/docker-zerotier-planet: 一分钟私有部署zerotier-planet服务", "url": "https://github.com/xubiaolin/docker-zerotier-planet?tab=readme-ov-file#45-openwrt-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE", "description": "来自 github.com 的书签", "category": "tool", "tags": ["GitHub"], "date_added": "2025-01-28T11:29:15Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "github.com"}, {"id": "42632426e7dd", "title": "Domain Name Search | Free Domain Availability Tool - Spaceship", "url": "https://www.spaceship.com/domain-search/?query=qiucat&beast=false&tab=domains", "description": "来自 www.spaceship.com 的书签", "category": "tool", "tags": ["Ai"], "date_added": "2025-02-03T01:01:29Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.spaceship.com"}, {"id": "583947c81841", "title": "【教程】小白也能看懂的自建Cloudflare临时邮箱教程（域名邮箱） - 文档共建 - LINUX DO", "url": "https://linux.do/t/topic/316819", "description": "来自 linux.do 的书签", "category": "tool", "tags": ["Linux", "教程"], "date_added": "2025-02-03T18:11:44Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "linux.do"}, {"id": "9ec236806254", "title": "根据站里佬友的步骤注册甲骨文成功了 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/420432", "description": "来自 linux.do 的书签", "category": "tool", "tags": ["Linux"], "date_added": "2025-02-11T12:57:38Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "linux.do"}, {"id": "5ef129dc5bba", "title": "Tesla M40 24G 相关调试及应用 - 哔哩哔哩", "url": "https://www.bilibili.com/opus/800273925016649729", "description": "来自 www.bilibili.com 的书签", "category": "tool", "tags": [], "date_added": "2025-02-15T17:36:37Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.bilibili.com"}, {"id": "4732acc29caa", "title": "x99主板四通道ddr3 ecc内存超频2133调时序，跑20万内存分，搭配简介使用。e5 2666v3、e5 2673v3跑分图_哔哩哔哩_bilibili", "url": "https://www.bilibili.com/video/BV1rqxqeYEff/?spm_id_from=333.337.search-card.all.click&vd_source=0730c911268ff523fed5714b68099a2d", "description": "来自 www.bilibili.com 的书签", "category": "tool", "tags": [], "date_added": "2025-02-16T23:45:58Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "www.bilibili.com"}, {"id": "4071ecee6cba", "title": "全国企业查询系统", "url": "https://javaw.icu/", "description": "来自 javaw.icu 的书签", "category": "tool", "tags": [], "date_added": "2025-03-17T13:24:11Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "javaw.icu"}, {"id": "cb3f83f3e9dc", "title": "剪映国际版", "url": "https://editor-api-sg.capcut.com/service/settings/v3/?device_platform=windows&channel=capcutpc_0&os_version=10.0.22631&pc_gl_version=3.0&aid=359289&version_code=198656&rom_version=1431&language=en®ion=CN", "description": "来自 editor-api-sg.capcut.com 的书签", "category": "tool", "tags": [], "date_added": "2025-05-15T09:21:28Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "editor-api-sg.capcut.com"}, {"id": "f26811318308", "title": "Download K-Lite Codec Pack", "url": "https://codecguide.com/download_kl.htm", "description": "来自 codecguide.com 的书签", "category": "tool", "tags": [], "date_added": "2025-06-06T16:01:40Z", "folder_path": ["书签栏", "Geek", "tool"], "domain": "codecguide.com"}], "VPS": [{"id": "fc939223fb74", "title": "云主机-控制台", "url": "https://console.cloud.tencent.com/cvm/index", "description": "来自 console.cloud.tencent.com 的书签", "category": "VPS", "tags": [], "date_added": "2018-03-09T16:56:00Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "console.cloud.tencent.com"}, {"id": "3ac6b69c3289", "title": "Hurricane Electric Hosted DNS", "url": "https://dns.he.net/index.cgi#", "description": "来自 dns.he.net 的书签", "category": "VPS", "tags": [], "date_added": "2019-08-10T00:00:03Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "dns.he.net"}, {"id": "6a3cf16b533e", "title": "多个地点Ping服务器,网站测速 - 站长工具", "url": "http://ping.chinaz.com/", "description": "来自 ping.chinaz.com 的书签", "category": "VPS", "tags": ["工具"], "date_added": "2020-03-28T00:16:02Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "ping.chinaz.com"}, {"id": "177aee0eb817", "title": "handsome主题", "url": "https://auth.ihewro.com/support/index.html", "description": "来自 auth.ihewro.com 的书签", "category": "VPS", "tags": [], "date_added": "2022-02-04T17:54:08Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "auth.ihewro.com"}, {"id": "12f4c568d07a", "title": "Xray X-ui可视化管理面板+宝塔面板 共存 nginx反代实现vless+vmess+websocks+tls - It小小鸟 itxiaoniao.cn", "url": "https://zszmm.com/archives/645/", "description": "来自 zszmm.com 的书签", "category": "VPS", "tags": [], "date_added": "2022-11-18T17:51:02Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "zszmm.com"}, {"id": "bfc03465a83b", "title": "服务器详情 - LightNode", "url": "https://console.lightnode.com/product/instance/detail?ecsResourceUUID=ecs-ww00006oy6ox®ionCode=my-kualalumpur-1&zoneCode=my-kualalumpur-1-a", "description": "来自 console.lightnode.com 的书签", "category": "VPS", "tags": [], "date_added": "2022-12-30T15:06:00Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "console.lightnode.com"}, {"id": "0fd9964275e1", "title": "管理产品 - VMISS", "url": "https://app.vmiss.com/clientarea.php?action=productdetails&id=9721", "description": "来自 app.vmiss.com 的书签", "category": "VPS", "tags": [], "date_added": "2023-01-16T02:08:37Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "app.vmiss.com"}, {"id": "e7ebf0cc4205", "title": "客戶中心 - RackNerd LLC", "url": "https://my.racknerd.com/clientarea.php?action=productdetails&id=278835", "description": "来自 my.racknerd.com 的书签", "category": "VPS", "tags": [], "date_added": "2023-11-12T01:25:09Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "my.racknerd.com"}, {"id": "cf2928644acd", "title": "bin456789/reinstall: 一键DD/重装脚本 (One-click reinstall OS on VPS)", "url": "https://github.com/bin456789/reinstall", "description": "来自 github.com 的书签", "category": "VPS", "tags": ["GitHub"], "date_added": "2024-07-12T11:20:32Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "github.com"}, {"id": "6840f60a39e4", "title": "萤光云管理控制台", "url": "https://console.ygcloud.com/dashboard/product/ecs/example", "description": "来自 console.ygcloud.com 的书签", "category": "VPS", "tags": [], "date_added": "2024-09-19T17:27:23Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "console.ygcloud.com"}, {"id": "2f8ac5f4ddb8", "title": "Fail2ban安装以及配置_fail2ban参数-CSDN博客", "url": "https://blog.csdn.net/dudu1225/article/details/121242917", "description": "来自 blog.csdn.net 的书签", "category": "VPS", "tags": ["Ai"], "date_added": "2024-09-20T16:49:49Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "blog.csdn.net"}, {"id": "127381ebaca8", "title": "London UK - VPS Servers - Kuroit VPS & Dedicated Servers Provider", "url": "https://my.kuroit.com/clientarea.php?action=productdetails&id=25175", "description": "来自 my.kuroit.com 的书签", "category": "VPS", "tags": [], "date_added": "2024-10-04T22:30:36Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "my.kuroit.com"}, {"id": "01cba2cb1970", "title": "WebRTC Leak Test - BrowserLeaks", "url": "https://browserleaks.com/webrtc", "description": "来自 browserleaks.com 的书签", "category": "VPS", "tags": [], "date_added": "2024-10-06T00:22:32Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "browserleaks.com"}, {"id": "f346b37f20aa", "title": "Ubuntu 20.04.4 Server 图文安装[含磁盘分区]_ubuntu2004server安装教程-CSDN博客", "url": "https://blog.csdn.net/llm_hao/article/details/124522423", "description": "来自 blog.csdn.net 的书签", "category": "VPS", "tags": ["教程"], "date_added": "2024-10-20T14:25:41Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "blog.csdn.net"}, {"id": "75d54b117e1a", "title": "【配置优化】我拿到VPS服务器必做的那些事 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/160305", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2024-11-10T00:38:42Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "4557f5304759", "title": "【全网首发】Xray通过反向代理使用别人的家庭宽带住宅IP、手机移动网络IP，获得任意国家最纯净的网络环境，跨境电商出海必备技能，windows住宅IP推荐vps，无限家宽住宅ipv6地址 - YouTube", "url": "https://www.youtube.com/watch?v=7VVMTX8iXbg&t=588s", "description": "来自 www.youtube.com 的书签", "category": "VPS", "tags": ["电商"], "date_added": "2024-11-27T09:40:56Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.youtube.com"}, {"id": "87a8b76dfb86", "title": "云服务器购买 - AkileCloud", "url": "https://akile.io/shop/server?type=traffic&areaId=3&nodeId=2&planId=864&aff_code=e72c3dfe-6c5d-4cbf-9fe5-1ca5c76d4373", "description": "来自 akile.io 的书签", "category": "VPS", "tags": [], "date_added": "2024-12-11T00:26:07Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "akile.io"}, {"id": "c26056bc366a", "title": "管理产品 - CLAWCLOUD", "url": "https://claw.cloud/clientarea.php?action=productdetails&id=38061", "description": "来自 claw.cloud 的书签", "category": "VPS", "tags": [], "date_added": "2025-02-19T22:48:25Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "claw.cloud"}, {"id": "63da2bbfae5d", "title": "泰国云服务器-泰国云主机-泰国VPS-朝暮数据", "url": "https://www.zhaomu.com/cloud-th-country", "description": "来自 www.zhaomu.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-02-22T11:48:57Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.zhaomu.com"}, {"id": "323c80f8021b", "title": "ReadyIDC : Service 24 Hour Support", "url": "https://customer.readyidc.com/index.php?/cart/", "description": "来自 customer.readyidc.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-02-22T12:02:31Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "customer.readyidc.com"}, {"id": "4dd6927344dc", "title": "每个国家和州（美国）的顶级网络托管公司", "url": "https://www.whtop.com/zh/companies", "description": "来自 www.whtop.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-02-22T12:42:30Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.whtop.com"}, {"id": "c7e236507b68", "title": "Debian 12 修改 SSH 登录端口和密码 - 𝐌𝐈𝐒𝐀𝐊𝐀.𝐄𝐒", "url": "https://misaka.es/archives/32.html", "description": "来自 misaka.es 的书签", "category": "VPS", "tags": [], "date_added": "2025-02-24T17:05:26Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "misaka.es"}, {"id": "454a8fdd859c", "title": "Facet Analysis", "url": "https://www.shodan.io/search/facet?facet=isp&query=http.html%3Aassets%2Fqs%2Fqs.min.js+country%3A", "description": "来自 www.shodan.io 的书签", "category": "VPS", "tags": [], "date_added": "2025-03-05T23:09:41Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.shodan.io"}, {"id": "b7bfa24fd1ed", "title": "IP Address Fraud Check", "url": "https://scamalytics.com/ip", "description": "来自 scamalytics.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-03-05T23:12:30Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "scamalytics.com"}, {"id": "8c1c61e76630", "title": "Trusted IP Data Provider, from IPv6 to IPv4 - IPinfo.io", "url": "https://ipinfo.io/", "description": "来自 ipinfo.io 的书签", "category": "VPS", "tags": [], "date_added": "2025-03-05T23:13:39Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "ipinfo.io"}, {"id": "df54950dab87", "title": "[优化]写给小白的自建2$/月的US原生家宽ip/HK节点解决方案 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/482315", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-03-10T19:00:49Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "5249fbe58d40", "title": "【FastGPT】【Dify】【n8n】教程帖资源合集 附公益API - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/458195", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux", "教程", "Api"], "date_added": "2025-03-15T10:23:02Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "2418c8dc3c64", "title": "NodeSeek", "url": "https://www.nodeseek.com/", "description": "来自 www.nodeseek.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-03T14:38:21Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.nodeseek.com"}, {"id": "9a912bb1de04", "title": "3x-ui/README.zh_CN.md at main · MHSanaei/3x-ui", "url": "https://github.com/MHSanaei/3x-ui/blob/main/README.zh_CN.md", "description": "来自 github.com 的书签", "category": "VPS", "tags": ["GitHub", "Ai"], "date_added": "2025-04-10T14:12:35Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "github.com"}, {"id": "9c847905af48", "title": "新标签页", "url": "https://console.evoxt.com/vmcontrolpanel.php?id=631168", "description": "来自 console.evoxt.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-10T21:06:33Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "console.evoxt.com"}, {"id": "6939222ca435", "title": "全球主机监控 - 国内外VPS、云服务器的库存监控和优惠信息", "url": "https://stock.hostmonit.com/", "description": "来自 stock.hostmonit.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-11T09:57:13Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "stock.hostmonit.com"}, {"id": "00a1d1b1b2ea", "title": "免费薅AWS云服务器羊毛的完整指南（2025年更新）", "url": "https://www.nodeseek.com/post-263015-1", "description": "来自 www.nodeseek.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-11T10:47:11Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.nodeseek.com"}, {"id": "a48d4d8c747f", "title": "OpenRouter", "url": "https://openrouter.ai/", "description": "来自 openrouter.ai 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-11T15:08:14Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "openrouter.ai"}, {"id": "2d4def84f354", "title": "webshare.io家宽包年8美元与clash链式代理部署教程，可以一定程度解决ChatGPT降智与注册较高风控账号 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/445909/11", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux", "教程"], "date_added": "2025-04-11T17:27:28Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "8ccf167b5ac8", "title": "我也来分享我注册乌龟的一些经验 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/447604/11", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-11T19:14:35Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "16c73e5812e2", "title": "IPRoyal | Premium Quality Proxies, Unbeatable Prices", "url": "https://iproyal.com/", "description": "来自 iproyal.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-11T19:34:42Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "iproyal.com"}, {"id": "3235bb496ee7", "title": "OpenWebUI优化: models 图像批量替换成 CDN 地址脚本分享 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/554075/6", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-12T10:25:43Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "036fd513e2d6", "title": "首页 - Canva可画", "url": "https://www.canva.com/", "description": "来自 www.canva.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-12T13:17:39Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.canva.com"}, {"id": "14bdeead7ada", "title": "Support - Infomaniak", "url": "https://manager.infomaniak.com/v3/1333125/ng/support-premium/dashboard", "description": "来自 manager.infomaniak.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-12T16:09:21Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "manager.infomaniak.com"}, {"id": "d39a41aea718", "title": "使用耗子面板搭建全功能彩虹聚合DNS管理系统教程 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/557175", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux", "教程"], "date_added": "2025-04-14T08:50:52Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "459e2c8f5fbb", "title": "有人想知道google永久免费小鸡，那我来讲讲吧 - 福利羊毛 / 福利羊毛, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/367915", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-14T11:55:53Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "affc13f2ee27", "title": "零基础学会如何创建第一个属于自己的chrome插件【定时刷新页面、防止WebRTC泄漏】 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/492063/6", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-15T13:16:06Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "a8f40695603a", "title": "✅Get a Free .EDU Email in 2025 | Step-by-Step Guide - YouTube", "url": "https://www.youtube.com/watch?v=Xz-qxShAb90&t=601s", "description": "来自 www.youtube.com 的书签", "category": "VPS", "tags": ["Ai"], "date_added": "2025-04-18T15:31:14Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.youtube.com"}, {"id": "69aedcc3b09a", "title": "泰国地址生成器 - 泰国身份生成器 - 泰国信用卡生成器", "url": "https://www.meiguodizhi.com/th-address", "description": "来自 www.meiguodizhi.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-18T17:53:49Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.meiguodizhi.com"}, {"id": "79f512a8c726", "title": "请稍候…", "url": "https://www.fakexy.com/", "description": "来自 www.fakexy.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-18T18:00:58Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.fakexy.com"}, {"id": "1a47f811d4fd", "title": "出生日期——美国身份查询", "url": "https://www.myheritage.com/names/john_branski", "description": "来自 www.myheritage.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-18T18:29:13Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.myheritage.com"}, {"id": "d0aacb69717b", "title": "netcup 复活节彩蛋射线-自动导航彩蛋！", "url": "https://www.nodeseek.com/post-317510-1", "description": "来自 www.nodeseek.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-18T19:49:26Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.nodeseek.com"}, {"id": "ec9b724661de", "title": "Fake Name Generator | FauxID.com", "url": "https://fauxid.com/fake-name-generator/united-states?age=18-24&gender=female", "description": "来自 fauxid.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-18T23:43:35Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "fauxid.com"}, {"id": "afbada073ff3", "title": "truepeoplesearch.com/find/person/pr9n0209lu2ur0200nuu", "url": "https://www.truepeoplesearch.com/find/person/pr9n0209lu2ur0200nuu", "description": "来自 www.truepeoplesearch.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-20T21:14:35Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.truepeoplesearch.com"}, {"id": "9d544cbca9ad", "title": "Dashboard - ProHosting24", "url": "https://prohosting24.de/cp/", "description": "来自 prohosting24.de 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-21T10:12:56Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "prohosting24.de"}, {"id": "8bd783cabe0f", "title": "HA SSD VPS - ProHosting24", "url": "https://prohosting24.de/cp/vserver/details/8721", "description": "来自 prohosting24.de 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-21T13:43:04Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "prohosting24.de"}, {"id": "e8c771ea211a", "title": "在debian12基础上安装proxmox8 | 求VPS", "url": "https://www.qiuvps.com/2030.html", "description": "来自 www.qiuvps.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-21T17:25:34Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.qiuvps.com"}, {"id": "9101f608f55c", "title": "帮你们节省85块钱 - 福利羊毛 / 福利羊毛, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/255722", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-22T12:48:53Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "86f0373ae4d9", "title": "UFW+IPSET：打造高效IP黑名单访问封锁利器 | 胡说八道", "url": "https://www.5dzone.com/posts/ufw-ipset%E6%89%93%E9%80%A0%E9%AB%98%E6%95%88ip%E9%BB%91%E5%90%8D%E5%8D%95%E8%AE%BF%E9%97%AE%E5%B0%81%E9%94%81%E5%88%A9%E5%99%A8.html", "description": "来自 www.5dzone.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-22T16:28:13Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.5dzone.com"}, {"id": "f58830d2b827", "title": "前言 | 一键虚拟化项目", "url": "https://www.spiritlhl.net/guide/pve/pve_precheck.html", "description": "来自 www.spiritlhl.net 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-24T01:01:41Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.spiritlhl.net"}, {"id": "3e7ae2b34fcf", "title": "一个真实地址生成器", "url": "https://www.nodeseek.com/post-320975-1", "description": "来自 www.nodeseek.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-24T08:39:22Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.nodeseek.com"}, {"id": "5508a51f3469", "title": "拒绝流量账单，利用哪吒面板来防止你的甲骨文、AWS等主机流量用超。 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/233349/15", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-24T15:22:42Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "d69cc3e325e9", "title": "🚀 使用 ClawCloud 部署 Sub-Store - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/593778", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-25T11:59:53Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "ca98b1f6e0b3", "title": "SJSU复活， San Jose State University， 权益汇总 - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/592434", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-25T13:00:30Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "bd6ae3aa5802", "title": "Sub Store 部署 - Lᴜᴄʏ's Tool", "url": "https://wiki.repcz.link/substore/install/", "description": "来自 wiki.repcz.link 的书签", "category": "VPS", "tags": [], "date_added": "2025-04-26T09:52:35Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "wiki.repcz.link"}, {"id": "c23fab5477e3", "title": "YJesus/Unhide: Stable version of Unhide", "url": "https://github.com/YJesus/Unhide", "description": "来自 github.com 的书签", "category": "VPS", "tags": ["GitHub"], "date_added": "2025-04-26T16:33:22Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "github.com"}, {"id": "0c7e955a6043", "title": "项目发布一天内破270+star 开心死了 发个帖先 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/604338/2", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-04-28T09:05:00Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "5ef9df282258", "title": "分享自己开发的开源指纹浏览器 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/611344/17", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux", "开源"], "date_added": "2025-04-29T22:26:50Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "93e3d8456482", "title": "使用 Rclone 挂载 OneDrive 自动备份 VPS 数据 - 我是怪兽", "url": "https://guaishoux.com/339.html", "description": "来自 guaishoux.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-05-03T21:14:00Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "guaishoux.com"}, {"id": "b35563<PERSON><PERSON>f6", "title": "教育部学籍在线验证报告编辑器", "url": "https://edu.yun7.de/", "description": "来自 edu.yun7.de 的书签", "category": "VPS", "tags": [], "date_added": "2025-05-08T07:27:37Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "edu.yun7.de"}, {"id": "66e4728708ce", "title": "ASU拿下Cursor Pro，相关步骤可以参考 - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/625491", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-05-08T08:06:52Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "ba740a2d2dce", "title": "分享1panel无代码部署qbittorrent+vertex进行PT刷流，把吃灰小鸡利用起来", "url": "https://www.nodeseek.com/post-305107-1", "description": "来自 www.nodeseek.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-06-25T12:08:43Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.nodeseek.com"}, {"id": "6e0d9e1a61cc", "title": "\"订阅\" - 猫猫博客", "url": "https://catcat.blog/?s=%E8%AE%A2%E9%98%85", "description": "来自 catcat.blog 的书签", "category": "VPS", "tags": [], "date_added": "2025-06-25T21:54:44Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "catcat.blog"}, {"id": "60b6b45448a8", "title": "https://github.com/google-gemini/gemini-cli", "url": "https://github.com/google-gemini/gemini-cli", "description": "来自 github.com 的书签", "category": "VPS", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2025-06-26T08:56:54Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "github.com"}, {"id": "e6dbc82669f3", "title": "Gemini 抓狂了 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/751070/4", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-06-26T19:22:44Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "5933cd5287bd", "title": "我用的微信，关 clash 什么事？ - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/749281/4", "description": "来自 linux.do 的书签", "category": "VPS", "tags": ["Linux"], "date_added": "2025-06-26T19:36:46Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linux.do"}, {"id": "efc4f1f26aa9", "title": "网络代理工具箱", "url": "https://www.haitunt.org/app.html", "description": "来自 www.haitunt.org 的书签", "category": "VPS", "tags": ["工具"], "date_added": "2025-07-10T12:14:49Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "www.haitunt.org"}, {"id": "810ea3ff24f6", "title": "XTLS/RealiTLScanner: A TLS server scanner for Reality", "url": "https://github.com/XTLS/RealiTLScanner", "description": "来自 github.com 的书签", "category": "VPS", "tags": ["GitHub"], "date_added": "2025-07-15T16:23:53Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "github.com"}, {"id": "e76cfcf34fb5", "title": "Debian/Ubuntu 中安装和配置 UFW（简单防火墙） - P3TERX ZONE", "url": "https://p3terx.com/archives/installing-and-configuring-ufw-in-debian.html", "description": "来自 p3terx.com 的书签", "category": "VPS", "tags": [], "date_added": "2025-07-24T09:45:42Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "p3terx.com"}, {"id": "7c9df0caad31", "title": "如何在 Debian 12 Linux 中向 sudo 组添加用户 - LinuxStory", "url": "https://linuxstory.org/how-to-add-a-user-to-sudo-group-in-debian-12-linux/", "description": "来自 linuxstory.org 的书签", "category": "VPS", "tags": [], "date_added": "2025-07-24T09:54:16Z", "folder_path": ["书签栏", "Geek", "VPS"], "domain": "linuxstory.org"}], "unraid": [{"id": "e47707ae9bc7", "title": "Unraid 篇三：导航页HomeLab全网最全测评_软件应用_什么值得买", "url": "https://post.smzdm.com/p/amxkop7p/", "description": "来自 post.smzdm.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-11T15:34:59Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "post.smzdm.com"}, {"id": "cd7381ef395b", "title": "unRAID Server Pro 6.11.5 скачать бесплатно. 70", "url": "https://softoroom.org/topic89043.html?pid=595807&st=70&#entry595807", "description": "来自 softoroom.org 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-03T14:58:01Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "softoroom.org"}, {"id": "c23543697792", "title": "[亲测可用]Unraid开心版6.10.3与安装教程 - 盒子萌", "url": "https://www.boxmoe.com/642.html", "description": "来自 www.boxmoe.com 的书签", "category": "unraid", "tags": ["教程", "Ai"], "date_added": "2023-02-03T15:35:17Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.boxmoe.com"}, {"id": "84100ab0efef", "title": "unraid下格式化硬盘 | 刘学馆 | nas相关笔记", "url": "http://www.x86.icu:6011/unraid/128.html", "description": "来自 www.x86.icu:6011 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-05T01:39:44Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.x86.icu:6011"}, {"id": "f729a1ccba39", "title": "求助！win11 wm 打开原神 显示虚拟机下无法运行 - Chinese / 简体中文 - Unraid", "url": "https://forums.unraid.net/topic/126098-%E6%B1%82%E5%8A%A9%EF%BC%81win11-wm-%E6%89%93%E5%BC%80%E5%8E%9F%E7%A5%9E-%E6%98%BE%E7%A4%BA%E8%99%9A%E6%8B%9F%E6%9C%BA%E4%B8%8B%E6%97%A0%E6%B3%95%E8%BF%90%E8%A1%8C/", "description": "来自 forums.unraid.net 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-06T03:29:14Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "forums.unraid.net"}, {"id": "ed15386143ff", "title": "UNRAID虚拟群晖918 6.2.3教程 - Tank 米多贝克", "url": "https://www.mi-d.cn/847", "description": "来自 www.mi-d.cn 的书签", "category": "unraid", "tags": ["教程", "Ai"], "date_added": "2023-02-07T02:25:52Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.mi-d.cn"}, {"id": "fc9642d804de", "title": "家用媒体服务器NAS 使用UNRAID系统的正确的玩法！直通网卡、直通硬盘、挂载群晖虚拟机文件！_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/awx0nglg/", "description": "来自 post.smzdm.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-09T00:30:04Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "post.smzdm.com"}, {"id": "c0af5ff4ec1c", "title": "unRaid 直通Win10显卡GT710 ，代码43 - Chinese / 简体中文 - Unraid", "url": "https://forums.unraid.net/topic/121428-unraid-%E7%9B%B4%E9%80%9Awin10%E6%98%BE%E5%8D%A1gt710-%EF%BC%8C%E4%BB%A3%E7%A0%8143/", "description": "来自 forums.unraid.net 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-10T11:50:03Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "forums.unraid.net"}, {"id": "7ed740405da3", "title": "unraid 自定义主题 CSS | 🌧️ 栗山未来", "url": "https://lswl.in/2022/04/23/unraid-theme-custom-css/", "description": "来自 lswl.in 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-10T16:57:16Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "lswl.in"}, {"id": "a64071eb10c7", "title": "UNRAID一篇就够！安装黑苹果macOS_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/axlzlnp3/", "description": "来自 post.smzdm.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-11T05:06:37Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "post.smzdm.com"}, {"id": "9e67bbe8f6dd", "title": "Unraid安装群晖DS918+7测试硬解和人脸识别 - 易波叶平", "url": "https://zhaouncle.com/unraid%E5%AE%89%E8%A3%85%E7%BE%A4%E6%99%96ds918-7%E6%B5%8B%E8%AF%95%E7%A1%AC%E8%A7%A3%E5%92%8C%E4%BA%BA%E8%84%B8%E8%AF%86%E5%88%AB/#33-%E5%9B%A0%E4%B8%BA-gpu-%E7%9A%84%E4%BD%8D%E7%BD%AE%E9%97%AE%E9%A2%98%E9%9C%80%E8%A6%81%E4%BF%AE%E6%94%B9%E4%BB%96%E7%9A%84%E6%80%BB%E7%BA%BF%E4%BD%8D%E7%BD%AE%E6%94%B9%E6%88%90%E7%BA%A2%E6%A1%86%E5%86%85%E7%9A%84%E6%A0%B7%E5%AD%90bus0x00-slot0x02%E5%90%A6%E5%88%99%E4%BC%9A%E5%AF%BC%E8%87%B4%E6%A0%B8%E6%98%BE%E8%AF%86%E5%88%AB%E6%9C%89%E9%97%AE%E9%A2%98", "description": "来自 zhaouncle.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-02-11T22:50:49Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "zhaouncle.com"}, {"id": "8580fa320deb", "title": "搞定unraid直通核显给WIN10后黑屏、声卡无输出问题", "url": "http://www.360doc.com/content/23/0119/16/4703094_1064214122.shtml", "description": "来自 www.360doc.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-03-02T21:59:37Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.360doc.com"}, {"id": "15aef5ff4c29", "title": "Unraid 篇一：Docker核显硬解 + windows虚拟机外接显示器物理输出iGPU YES!_显示器_什么值得买", "url": "https://post.smzdm.com/p/apv7l397/", "description": "来自 post.smzdm.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-03-03T02:55:19Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "post.smzdm.com"}, {"id": "cd53d9c456de", "title": "nas 篇一：unraid折腾之win10直通“唯一”独立显卡", "url": "http://www.360doc.com/content/23/0119/15/4703094_1064212787.shtml", "description": "来自 www.360doc.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-03-03T09:53:47Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.360doc.com"}, {"id": "91b226c9e7b4", "title": "NVIDIA 驱动和 CUDA 版本信息速查 // 杰哥的{运维,编程,调板子}小笔记", "url": "https://jia.je/software/2021/12/26/nvidia-cuda/", "description": "来自 jia.je 的书签", "category": "unraid", "tags": [], "date_added": "2023-03-04T02:33:35Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "jia.je"}, {"id": "f74ad162fbbc", "title": "Advanced GPU passthrough techniques on Unraid - YouTube", "url": "https://www.youtube.com/watch?v=QlTVANDndpM", "description": "来自 www.youtube.com 的书签", "category": "unraid", "tags": ["Ai"], "date_added": "2023-03-06T22:50:10Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.youtube.com"}, {"id": "7326a2721772", "title": "硬件笔记之GP106-90 3GB GTX1060 3GB魔改 - 腾讯云开发者社区-腾讯云", "url": "https://cloud.tencent.com/developer/article/1675546", "description": "来自 cloud.tencent.com 的书签", "category": "unraid", "tags": [], "date_added": "2023-03-07T16:14:29Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "cloud.tencent.com"}, {"id": "6d816cbeba87", "title": "Tesla M40 vGPU Proxmox 7.1 | ZemaToxic's Dev Blog", "url": "https://blog.zematoxic.com/06/03/2022/Tesla-M40-vGPU-Proxmox-7-1/", "description": "来自 blog.zematoxic.com 的书签", "category": "unraid", "tags": [], "date_added": "2023-03-25T16:59:22Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "blog.zematoxic.com"}, {"id": "2ae8ae8d893b", "title": "Proxmox VE直通N卡Code43解决 - hello world!", "url": "https://echo.gg/2022/03/343/#post-images", "description": "来自 echo.gg 的书签", "category": "unraid", "tags": [], "date_added": "2023-03-25T19:36:06Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "echo.gg"}, {"id": "1247ae4dbe5f", "title": "Tesla M40 使用分享_tesla m40 双卡_MarineCode的博客-CSDN博客", "url": "https://blog.csdn.net/m0_47455189/article/details/126432198", "description": "来自 blog.csdn.net 的书签", "category": "unraid", "tags": [], "date_added": "2023-03-25T20:37:25Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "blog.csdn.net"}, {"id": "c9e9fd3c6854", "title": "《ai绘画》最全stable diffusion安装教程 所有软件安装和指令 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv22011634?from=articleDetail", "description": "来自 www.bilibili.com 的书签", "category": "unraid", "tags": ["教程", "Ai"], "date_added": "2023-03-25T23:16:41Z", "folder_path": ["书签栏", "Geek", "unraid"], "domain": "www.bilibili.com"}], "NAS": [{"id": "207939cb2439", "title": "群晖全新部署傻妞 对接芝士、公众号完整教程-牧之笔记 - 世界不应有局限", "url": "https://www.mspace.cc/archives/511", "description": "来自 www.mspace.cc 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-02-18T19:50:46Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.mspace.cc"}, {"id": "2ff7946bc244", "title": "Mikan Project - 我的番组", "url": "https://mikanani.me/Home/MyBangumi", "description": "来自 mikanani.me 的书签", "category": "NAS", "tags": [], "date_added": "2022-12-06T11:56:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "mikanani.me"}, {"id": "9d7ddb888560", "title": "arpl編譯下載速度過慢？修改兩個文件拯救你的下載速度", "url": "https://inewsdb.com/%e6%95%b8%e7%a2%bc/arpl%e7%b7%a8%e8%ad%af%e4%b8%8b%e8%bc%89%e9%80%9f%e5%ba%a6%e9%81%8e%e6%85%a2%ef%bc%9f%e4%bf%ae%e6%94%b9%e5%85%a9%e5%80%8b%e6%96%87%e4%bb%b6%e6%8b%af%e6%95%91%e4%bd%a0%e7%9a%84%e4%b8%8b%e8%bc%89/", "description": "来自 inewsdb.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-07T01:56:42Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "inewsdb.com"}, {"id": "28ff6d0456ba", "title": "群晖DSM 7使用Docker安装ZeroTier", "url": "https://www.itblogcn.com/article/1818.html", "description": "来自 www.itblogcn.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-07T04:46:38Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.itblogcn.com"}, {"id": "4cb3de9dc39c", "title": "群晖算号器(全洗白)", "url": "http://api.ssr0.cn:8000/sn?model=DS918&sn=17A0PDN226500&mac=001132829B71", "description": "来自 api.ssr0.cn:8000 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-09T00:07:08Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "api.ssr0.cn:8000"}, {"id": "3459009b7a75", "title": "群晖Docker：小白安装tmm刮削保姆级教程，修改host解决刮削不全 ，建立完美电影墙！_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/ar0nq5dg/", "description": "来自 post.smzdm.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-02-09T17:13:16Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "post.smzdm.com"}, {"id": "fcdf92c6a089", "title": "群晖上通过RcloneBrowser挂载云盘 | 老苏的blog", "url": "https://laosu.ml/2021/06/21/%E7%BE%A4%E6%99%96%E4%B8%8A%E9%80%9A%E8%BF%87RcloneBrowser%E6%8C%82%E8%BD%BD%E4%BA%91%E7%9B%98/", "description": "来自 laosu.ml 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-09T19:33:00Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "laosu.ml"}, {"id": "84a982d17c95", "title": "《群晖》Rclone 安装配置教程 - 连接 OneDrive - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv16130034?from=search", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-02-09T21:00:56Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "4619c8997e2b", "title": "将阿里云盘挂载为Webdav并使用rclone挂载到本地 – 南猫", "url": "https://southcat.net/2811.html", "description": "来自 southcat.net 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-09T21:25:44Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "southcat.net"}, {"id": "d45bb1110ba0", "title": "Alist使用Cloudflare workers为OneDrive加速 - fl0w1nd‘s blog", "url": "https://blog.itleaf.xyz/technology/8.html", "description": "来自 blog.itleaf.xyz 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-09T21:43:42Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.itleaf.xyz"}, {"id": "63e470b5d4c2", "title": "群晖DSM7.0使用zerotier套件的简单办法 - 我不是矿神", "url": "https://imnks.com/3175.html", "description": "来自 imnks.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-10T00:15:19Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "imnks.com"}, {"id": "b7fe336825d9", "title": "群晖6.1+6.2 docker开启局域网桥接并创建过滤广告容器-云深不知处", "url": "https://wps.520810.xyz:666/?p=752", "description": "来自 wps.520810.xyz:666 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-12T23:36:39Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "wps.520810.xyz:666"}, {"id": "f9732d83d3eb", "title": "群晖必备的一些套件安装 - 科技玩家", "url": "https://www.kejiwanjia.com/jiaocheng/zheteng/notes/42781.html", "description": "来自 www.kejiwanjia.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-13T17:58:30Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.kejiwanjia.com"}, {"id": "7c04a1857d9c", "title": "群辉DSM7.0.1安装bootstrap后解决wget: error while loading shared libraries: libgnuintl.so.8: cannot open shared object file: No such file or directory - 科技玩家", "url": "https://www.kejiwanjia.com/jiaocheng/62293.html", "description": "来自 www.kejiwanjia.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-13T17:58:35Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.kejiwanjia.com"}, {"id": "d2ccb8e178be", "title": "estrellaxd/auto_bangumi - Docker Image | Docker Hub", "url": "https://hub.docker.com/r/estrellaxd/auto_bangumi", "description": "来自 hub.docker.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-13T23:52:25Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "hub.docker.com"}, {"id": "167bcc7774a5", "title": "GXNAS博客 - https://wp.gxnas.com", "url": "https://wp.gxnas.com/", "description": "来自 wp.gxnas.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-02-18T19:41:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "wp.gxnas.com"}, {"id": "e6240bd3d8e7", "title": "NAS备忘录 篇三十九：UNRAID 6.11 虚拟机安装 WIN 10，并虚拟化核显（十代 QSRL CPU）_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/a7nkr2po/", "description": "来自 post.smzdm.com 的书签", "category": "NAS", "tags": ["Ai"], "date_added": "2023-03-02T16:40:58Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "post.smzdm.com"}, {"id": "fc051f68530b", "title": "UNRAID 6.11 虚拟机安装 WIN 10，并虚拟化核显（十代 QSRL CPU）|插件|英特尔|win10|unraid|cpu_网易订阅", "url": "https://www.163.com/dy/article/HMO3ET0G053186JB.html", "description": "来自 www.163.com 的书签", "category": "NAS", "tags": ["Ai"], "date_added": "2023-03-02T21:08:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.163.com"}, {"id": "74fd41c26dbb", "title": "Unraid 篇一：Unraid 安装群晖 DS918+7 测试硬解和人脸识别_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/a5dl2808/", "description": "来自 post.smzdm.com 的书签", "category": "NAS", "tags": ["Ai"], "date_added": "2023-03-04T19:48:22Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "post.smzdm.com"}, {"id": "00dc317ae318", "title": "P106解锁版驱动一键安装包（更新）【p106吧】_百度贴吧", "url": "https://tieba.baidu.com/p/8178312322", "description": "来自 tieba.baidu.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-04T21:37:16Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "tieba.baidu.com"}, {"id": "8c41b40f036e", "title": "Win11系统安装过程中如何跳过网络连接？_哔哩哔哩_bilibili", "url": "https://www.bilibili.com/video/BV13G411E7HQ/?spm_id_from=333.788.recommend_more_video.12&vd_source=0730c911268ff523fed5714b68099a2d", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-06T21:13:19Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "cf4925ec390e", "title": "佛西博客", "url": "https://foxi.buduanwang.vip/", "description": "来自 foxi.buduanwang.vip 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-21T09:15:04Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "foxi.buduanwang.vip"}, {"id": "c74884ff00fd", "title": "vGPU教程 - 国光的 PVE 环境搭建教程", "url": "https://pve.sqlsec.com/4/5/#_11", "description": "来自 pve.sqlsec.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-03-21T18:13:02Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "pve.sqlsec.com"}, {"id": "eeb40bb05ea8", "title": "Tesla M40 训练机组装与散热改造_里先森的博客-CSDN博客", "url": "https://blog.csdn.net/sements/article/details/125256812?ops_request_misc=%257B%2522request%255Fid%2522%253A%2522166531170816800182740292%2522%252C%2522scm%2522%253A%252220140713.130102334%E2%80%A6%2522%257D&request_id=166531170816800182740292&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2allsobaiduend~default-1-125256812-null-null.142v52pc_rank_34_1,201v3control_1&utm_term=tesla%20m40&spm=1018.2226.3001.4187", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-22T15:22:27Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "af6ba187bbb4", "title": "NVIDIA TESLA M40 24G的奇妙游戏之旅 – Fantasy Land", "url": "https://east.moe/archives/1264", "description": "来自 east.moe 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-22T16:17:31Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "east.moe"}, {"id": "6333004b64df", "title": "Activating a Secondary Display on Windows 10 when no Monitor is Connected - Amyuni Technologies", "url": "https://www.amyuni.com/forum/viewtopic.php?t=3030", "description": "来自 www.amyuni.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-22T16:36:29Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.amyuni.com"}, {"id": "7de46eaecc6e", "title": "Tesla P40 计算卡 与 GTX750 亮机卡 宠粉远程服务实录 已授权录制_哔哩哔哩_bilibili", "url": "https://www.bilibili.com/video/BV13W4y1s7so/?spm_id_from=333.788.recommend_more_video.14&vd_source=0730c911268ff523fed5714b68099a2d", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-23T14:13:21Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "31fc7965fb3a", "title": "PVE安装Win10并直通显卡(独显)", "url": "https://iamroot.cn/pvean-zhuang-win/", "description": "来自 iamroot.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-24T21:40:48Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "iamroot.cn"}, {"id": "57cc3d5bc342", "title": "i 硬件直通及显卡直通配置 · ivanhao/pvetools Wiki · GitHub", "url": "https://github.com/ivanhao/pvetools/wiki/i-%E7%A1%AC%E4%BB%B6%E7%9B%B4%E9%80%9A%E5%8F%8A%E6%98%BE%E5%8D%A1%E7%9B%B4%E9%80%9A%E9%85%8D%E7%BD%AE", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2023-03-25T13:54:25Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "3edd0ece742a", "title": "Proxmox VE (Tesla P40) vGPU 配置 - azhuge233's", "url": "https://azhuge233.com/proxmox-ve-tesla-p40-vgpu-%e9%85%8d%e7%bd%ae/", "description": "来自 azhuge233.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-25T14:06:24Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "azhuge233.com"}, {"id": "a48a83ba23cf", "title": "Proxmox PVE ISO镜像所有版本下载 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv21158501", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-25T16:50:59Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "eb75ee2d32f9", "title": "Proxmox VE（PVE）中黑群晖DSM7.X引导编译与安装 - 佐蓝小站", "url": "https://blog.gurenkai.com:11443/archives/install-pve-dsm", "description": "来自 blog.gurenkai.com:11443 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-26T14:23:48Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.gurenkai.com:11443"}, {"id": "2021f7668829", "title": "PVE系列教程(十四)、安装黑苹果最新系统MacOS Monterey(BigSur、Mont", "url": "https://weibo.com/ttarticle/p/show?id=2309404877088791593035#_loginLayer_1680108137779", "description": "来自 weibo.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-03-30T01:51:37Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "weibo.com"}, {"id": "19f03225ae3c", "title": "PVE 虚拟化黑苹果显卡直通及远程访问教程", "url": "https://blog.lv5.moe/p/pve-virtualized-hackintosh-gpu-passthrough-and-remote-access-tutorial#%E5%88%86%E9%85%8D%E6%98%BE%E5%8D%A1", "description": "来自 blog.lv5.moe 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-03-30T03:03:24Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.lv5.moe"}, {"id": "9c06b212378d", "title": "手把手基于Proxmox VE安装macOS Monterey【黑苹果】 - 掘金", "url": "https://juejin.cn/post/7158442404188520484", "description": "来自 juejin.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-31T16:33:36Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "juejin.cn"}, {"id": "71e44463973c", "title": "青龙面板最全依赖2022-3-31更新-CSDN博客", "url": "https://blog.csdn.net/Lihuos/article/details/122869207", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-02T18:15:12Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "76ccb3b309cc", "title": "Proxmox VE 7.1安装macOS 10.15及GPU穿通方案 - 分贝网", "url": "https://db.ci/daily/5293.html", "description": "来自 db.ci 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-28T18:43:08Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "db.ci"}, {"id": "f4f67a3319e7", "title": "| APQA镜像站", "url": "https://mirrors.apqa.cn/proxmox-edge/officialiso/", "description": "来自 mirrors.apqa.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-07T02:10:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "mirrors.apqa.cn"}, {"id": "6c7906050318", "title": "佛西博客 - pve虚拟机VGPU方案简析", "url": "https://foxi.buduanwang.vip/vdi/573.html/", "description": "来自 foxi.buduanwang.vip 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-07T20:43:31Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "foxi.buduanwang.vip"}, {"id": "ffaa1d648559", "title": "PolloLoco / NVIDIA vGPU Guide · GitLab", "url": "https://gitlab.com/polloloco/vgpu-proxmox", "description": "来自 gitlab.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-07T21:50:01Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "gitlab.com"}, {"id": "746d4de2dbca", "title": "vGPU教程 - 国光的 PVE 环境搭建教程", "url": "https://pve.sqlsec.com/4/5/", "description": "来自 pve.sqlsec.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-04-09T01:26:05Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "pve.sqlsec.com"}, {"id": "047dda576689", "title": "Release NVIDIA vGPU Drivers 14.4 · justin-himself/NVIDIA-VGPU-Driver-Archive", "url": "https://github.com/justin-himself/NVIDIA-VGPU-Driver-Archive/releases/tag/14.4", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub"], "date_added": "2023-04-10T15:30:28Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "1616212edcce", "title": "collinwebdesigns/fastapi-dls - Docker Image | Docker Hub", "url": "https://hub.docker.com/r/collinwebdesigns/fastapi-dls", "description": "来自 hub.docker.com 的书签", "category": "NAS", "tags": ["Api"], "date_added": "2023-04-10T19:32:08Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "hub.docker.com"}, {"id": "79fc4c06726a", "title": "国光的 PVE 生产环境配置优化记录 | 国光", "url": "https://www.sqlsec.com/2022/04/pve.html#PVE-%E9%BB%91%E8%8B%B9%E6%9E%9C%EF%BC%88%E4%B8%8A%EF%BC%89", "description": "来自 www.sqlsec.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-11T01:19:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.sqlsec.com"}, {"id": "e30db6e33c22", "title": "群晖 Docker 搭建 Clash 订阅转换平台教程-牧之笔记 - 世界不应有局限", "url": "https://www.mspace.cc/archives/373", "description": "来自 www.mspace.cc 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-04-12T00:21:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.mspace.cc"}, {"id": "65bdebeb37db", "title": "RDP 优化操作 微软远程桌面 开启显卡加速、60FPS、USB设备重定向 | 橙叶博客", "url": "https://www.orgleaf.com/3771.html", "description": "来自 www.orgleaf.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-12T14:12:42Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.orgleaf.com"}, {"id": "759f1675c5e4", "title": "不同版本cuda对应的NVIDIA驱动版本_cuda 驱动版本对应_sundaygeek的博客-CSDN博客", "url": "https://blog.csdn.net/mouse1598189/article/details/86695400", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-17T18:32:17Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "f10db85cf2f5", "title": "PVE中如何设置对虚拟机进行去虚拟化 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv22361926/", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-24T13:40:58Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "d1f5d90c6f49", "title": "NVIDIA 英伟达TESLA计算卡专用驱动 GRID驱动下载链接分享（P40 P100 M40等显卡可用） - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv19405017", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-28T05:40:30Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "6e0a9f24ea0f", "title": "Linux上如何使用Stable Diffusion WebUI - 掘金", "url": "https://juejin.cn/post/7208946311886372924", "description": "来自 juejin.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-28T19:14:57Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "juejin.cn"}, {"id": "e6a5ce84f8a2", "title": "Proxmox VE(PVE)虚拟机绕过软件的虚拟机检查来玩原神 - 某咸鱼的笔记", "url": "https://www.wunote.cn/article/4801/", "description": "来自 www.wunote.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-04-29T15:34:59Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.wunote.cn"}, {"id": "1749aa975268", "title": "ProxmoxVE安装VGPU – 心野小站", "url": "https://www.xyxb.vip/?p=380", "description": "来自 www.xyxb.vip 的书签", "category": "NAS", "tags": [], "date_added": "2023-05-04T03:17:34Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.xyxb.vip"}, {"id": "e6a013bf5c0b", "title": "Linux服务器安装cuda,cudnn，显卡驱动和pytorch超详细流程_kingfoulin的博客-CSDN博客", "url": "https://blog.csdn.net/kingfoulin/article/details/98872965", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2023-05-04T04:22:45Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "d9f76be301c5", "title": "2023黑苹果Big Sur/Monterey免驱原生支持独立显卡购买指南 | 1GPU", "url": "https://www.1gpu.com/553.html", "description": "来自 www.1gpu.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-03-30T12:45:54Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.1gpu.com"}, {"id": "0a53d1bc9c08", "title": "alist.nn.ci/zh/guide/", "url": "https://alist.nn.ci/zh/guide/", "description": "来自 alist.nn.ci 的书签", "category": "NAS", "tags": [], "date_added": "2023-08-27T17:50:20Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "alist.nn.ci"}, {"id": "3eb784f8597f", "title": "巧用docker里的ubuntu，跑傻妞、oicq (onebot:再见👋🏻) | 梦磊の博客", "url": "https://ymlclub.cn/2021121119419/", "description": "来自 ymlclub.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-08-27T19:41:02Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "ymlclub.cn"}, {"id": "f28e0e3f01bb", "title": "Synology 群晖 Nvidia vGPU/GPU 实践指南-蔚然小站", "url": "https://blog.kkk.rs/archives/12#%E5%AE%89%E8%A3%85%E8%BF%87%E7%A8%8B", "description": "来自 blog.kkk.rs 的书签", "category": "NAS", "tags": [], "date_added": "2023-10-13T13:25:04Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.kkk.rs"}, {"id": "8dec62d3ee92", "title": "List of working motherboards · Issue #11 · xCuri0/ReBarUEFI", "url": "https://github.com/xCuri0/ReBarUEFI/issues/11", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub"], "date_added": "2023-11-10T18:00:16Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "f2773c0f9de2", "title": "群晖算号器(全洗白)", "url": "http://api.ssr0.cn:8000/sn?model=DS918&sn=17A0PDN319001&mac=00113282A3FB", "description": "来自 api.ssr0.cn:8000 的书签", "category": "NAS", "tags": [], "date_added": "2023-11-12T21:33:26Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "api.ssr0.cn:8000"}, {"id": "76b64afcd53b", "title": "vGPU在Proxmox VE下的配置与使用 – 雪林荧光", "url": "https://xinalin.com/159/vgpu-configuration-on-pve", "description": "来自 xinalin.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-11-15T12:35:41Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "xinalin.com"}, {"id": "1fd583a0bb6e", "title": "nVidia PCI id database — envytools git documentation", "url": "https://envytools.readthedocs.io/en/latest/hw/pciid.html#nv10", "description": "来自 envytools.readthedocs.io 的书签", "category": "NAS", "tags": [], "date_added": "2023-11-15T12:45:13Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "envytools.readthedocs.io"}, {"id": "83d2a9592d2d", "title": "我有一个机器人，可骂街、舔狗、土味、发扔子 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv21285551/", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2023-08-27T18:53:00Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "0d9826db6b16", "title": "硬件直通 - 国光的 PVE 环境搭建教程", "url": "https://pve.sqlsec.com/4/2/", "description": "来自 pve.sqlsec.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-11-26T02:38:24Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "pve.sqlsec.com"}, {"id": "1e42d0398942", "title": "pve7 pve8 kvmqemu反虚拟化检测显卡直通玩游戏教程小白直接安装+大神可以自己源码编译 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv26245305/", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2023-11-30T00:21:23Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "2e170fb35404", "title": "PVE开启硬件直通功能", "url": "http://linux.it.net.cn/e/Virtualization/Proxmox/2023/0306/31611.html", "description": "来自 linux.it.net.cn 的书签", "category": "NAS", "tags": [], "date_added": "2023-12-02T23:01:01Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "linux.it.net.cn"}, {"id": "65a275e5f36b", "title": "APQA网盘 - /vGPU/17.0/", "url": "https://foxi.buduanwang.vip/pan/vGPU/17.0/", "description": "来自 foxi.buduanwang.vip 的书签", "category": "NAS", "tags": [], "date_added": "2024-03-11T23:04:28Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "foxi.buduanwang.vip"}, {"id": "283da91dfe1e", "title": "Releases · RROrg/rr", "url": "https://github.com/RROrg/rr/releases", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub"], "date_added": "2024-04-05T13:37:09Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "4884b6d4e3fb", "title": "解决---设备“VMnet0”上的网桥没有运行。该虚拟机无法与此主机或网络上的其他主机进行通信。 无法连接虚拟设备“Ethernet0”。-CSDN博客", "url": "https://blog.csdn.net/Wysnbb/article/details/123996480", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2024-04-15T19:59:07Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "cdcdd875b4f2", "title": "alist-tvbox/doc/README_zh.md at master · power721/alist-tvbox · GitHub", "url": "https://github.com/power721/alist-tvbox/blob/master/doc/README_zh.md", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2024-04-15T23:20:10Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "873e7d3f2e86", "title": "带8个免费监控授权、支持独显硬解的黑群晖DSM7.X，想要不？ - GXNAS博客", "url": "https://wp.gxnas.com/12534.html", "description": "来自 wp.gxnas.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-07-12T22:55:05Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "wp.gxnas.com"}, {"id": "eb0f943ecb91", "title": "使用VirtualBox虚拟机安装群晖7.1系统 | 牧尘的NAS小站", "url": "https://www.dreamlyn.cn/1751037144.html", "description": "来自 www.dreamlyn.cn 的书签", "category": "NAS", "tags": [], "date_added": "2024-07-27T11:39:55Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.dreamlyn.cn"}, {"id": "672dfc125239", "title": "让VirtualBox虚拟机实现开机自动后台运行 | 四少爷的blog", "url": "https://www.jermey.cn/2021/06/05/1.html", "description": "来自 www.jermey.cn 的书签", "category": "NAS", "tags": [], "date_added": "2024-07-27T11:49:21Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.jermey.cn"}, {"id": "50a39b764b25", "title": "TANK电玩 公开群晖半洗白码 - 米多贝克&米多网络工程", "url": "https://mi-d.cn/4430", "description": "来自 mi-d.cn 的书签", "category": "NAS", "tags": [], "date_added": "2024-07-27T17:08:38Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "mi-d.cn"}, {"id": "2e7ceff1742b", "title": "VMware虚拟机安装黑群晖系统笔记_林鸿风采的技术博客_51CTO博客", "url": "https://blog.51cto.com/linhong/10445812", "description": "来自 blog.51cto.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-05T03:51:54Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.51cto.com"}, {"id": "2c4606bd646a", "title": "VMware安装Ubuntu(2024最新最全版)-CSDN博客", "url": "https://blog.csdn.net/fanyun_01/article/details/136540798", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-05T05:43:34Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "f5e11c1e98a3", "title": "佛西博客 - 来自民间的VGPU授权fastapi-dls", "url": "https://foxi.buduanwang.vip/virtualization/pve/2195.html/", "description": "来自 foxi.buduanwang.vip 的书签", "category": "NAS", "tags": ["Api"], "date_added": "2024-10-14T02:00:54Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "foxi.buduanwang.vip"}, {"id": "0c06b94b4b3b", "title": "显卡虚拟化，Tesla P4在PVE8下的vgpu配置方案，兼容多显卡直通 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv33513142/", "description": "来自 www.bilibili.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-14T02:10:18Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.bilibili.com"}, {"id": "dc22bb9816a4", "title": "文件 · driver-550.90.05 · <PERSON><PERSON><PERSON><PERSON> / ELKH Cloud · GitLab", "url": "https://gitlab.wigner.hu/pinter.adam/elkh-cloud/-/tree/driver-550.90.05?ref_type=heads", "description": "来自 gitlab.wigner.hu 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-14T02:18:17Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "gitlab.wigner.hu"}, {"id": "3232051f9532", "title": "virtio-win/virtio-win-pkg-scripts: Scripts for packaging virtio-win drivers", "url": "https://github.com/virtio-win/virtio-win-pkg-scripts?tab=readme-ov-file", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub"], "date_added": "2024-10-14T02:32:30Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "600a3e0fc409", "title": "windows上安装miniforge和jupyterlab_miniforge windows-CSDN博客", "url": "https://blog.csdn.net/<PERSON><PERSON><PERSON>_<PERSON>/article/details/139124165", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-16T19:25:33Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "8fecd3aa5fc4", "title": "How to run a jupyter notebook through a remote server on local machine? - Stack Overflow", "url": "https://stackoverflow.com/questions/69244218/how-to-run-a-jupyter-notebook-through-a-remote-server-on-local-machine", "description": "来自 stackoverflow.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-25T16:23:02Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "stackoverflow.com"}, {"id": "992166b5cc82", "title": "驱动和其他镜像 | AList", "url": "https://index.mitsea.com/%E8%BD%AF%E4%BB%B6/%E9%A9%B1%E5%8A%A8%E5%92%8C%E5%85%B6%E4%BB%96%E9%95%9C%E5%83%8F", "description": "来自 index.mitsea.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-25T21:42:56Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "index.mitsea.com"}, {"id": "1932e325f8ca", "title": "适用于 NVIDIA RTX 虚拟工作站 (vWS) 的驱动程序  |  Compute Engine Documentation  |  Google Cloud", "url": "https://cloud.google.com/compute/docs/gpus/grid-drivers-table?hl=zh-cn", "description": "来自 cloud.google.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-25T22:05:29Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "cloud.google.com"}, {"id": "8f617c105506", "title": "[Android] ubuntu虚拟机上搭建 Waydroid 环境-CSDN博客", "url": "https://blog.csdn.net/ykun089/article/details/135049480", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": [], "date_added": "2024-10-26T09:40:48Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "5df6624b237e", "title": "解决Proxmox VE虚拟机无法运行原神一例 | Bug侠", "url": "https://bugxia.com/3600.html", "description": "来自 bugxia.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-11-04T04:20:23Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "bugxia.com"}, {"id": "9b5637135bdc", "title": "No.1 简单搭建 Zerotier Moon 为虚拟网络加速 | tvtv.fun", "url": "https://tvtv.fun/vps/001.html", "description": "来自 tvtv.fun 的书签", "category": "NAS", "tags": [], "date_added": "2024-11-04T17:40:16Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "tvtv.fun"}, {"id": "1e554550ca23", "title": "July's云盘", "url": "https://yun.yangwenqing.com/ESXI_PVE", "description": "来自 yun.yangwenqing.com 的书签", "category": "NAS", "tags": [], "date_added": "2024-11-10T20:30:27Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "yun.yangwenqing.com"}, {"id": "ae9faf732768", "title": "2024最新PVE安装黑群晖系统教程 - 折腾笔记", "url": "https://blog.zwbcc.cn/archives/1710522887031", "description": "来自 blog.zwbcc.cn 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2025-02-16T22:59:09Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.zwbcc.cn"}, {"id": "bcaad5f67ccc", "title": "pve-anti-detection/README.md at main · lixiaoliu666/pve-anti-detection · GitHub", "url": "https://github.com/lixiaoliu666/pve-anti-detection/blob/main/README.md", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub", "Ai", "<PERSON><PERSON><PERSON>"], "date_added": "2025-03-09T21:30:25Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "1e15c1920962", "title": "Proxmox VE 8.1 vGPU 配置 （A6000）", "url": "https://blog.mitsea.com/d29bb28b14984443b232263348b946ba/", "description": "来自 blog.mitsea.com 的书签", "category": "NAS", "tags": [], "date_added": "2025-05-16T22:44:54Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.mitsea.com"}, {"id": "392d04a06911", "title": "PVE直通显卡 & Intel SRIOV - MAENE - 博客园", "url": "https://www.cnblogs.com/MAENESA/p/18005241", "description": "来自 www.cnblogs.com 的书签", "category": "NAS", "tags": [], "date_added": "2025-05-16T23:12:35Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "www.cnblogs.com"}, {"id": "3c86d7ef5072", "title": "Proxmox VE直通硬盘（全盘映射方式） | 自说Me话", "url": "https://isay.me/2024/04/pve-harddisk-passthrough.html", "description": "来自 isay.me 的书签", "category": "NAS", "tags": [], "date_added": "2025-05-17T14:21:39Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "isay.me"}, {"id": "ccaa97150ac6", "title": "佛西博客 - Proxmox VE pve硬盘直通", "url": "https://foxi.buduanwang.vip/virtualization/1754.html/", "description": "来自 foxi.buduanwang.vip 的书签", "category": "NAS", "tags": [], "date_added": "2025-05-17T14:21:47Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "foxi.buduanwang.vip"}, {"id": "1b8431e6f2d3", "title": "总结几点Wake On Lan(WOL)失败的原因 · Issue #124 · Bpazy/blog", "url": "https://github.com/Bpazy/blog/issues/124", "description": "来自 github.com 的书签", "category": "NAS", "tags": ["GitHub"], "date_added": "2025-05-18T11:27:07Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "github.com"}, {"id": "829a35cf4e71", "title": "NVIDIA 驱动和 CUDA 版本信息速查 - 杰哥的{运维，编程，调板子}小笔记", "url": "https://jia.je/software/2021/12/26/nvidia-cuda/#%E5%B8%B8%E7%94%A8%E5%9C%B0%E5%9D%80", "description": "来自 jia.je 的书签", "category": "NAS", "tags": [], "date_added": "2025-05-18T13:39:45Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "jia.je"}, {"id": "8357aa13fed1", "title": "【保姆级教程】Windows安装CUDA及cuDNN_windows安装cudnn-CSDN博客", "url": "https://blog.csdn.net/qq_40968179/article/details/128996692", "description": "来自 blog.csdn.net 的书签", "category": "NAS", "tags": ["教程"], "date_added": "2025-05-18T14:29:48Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "blog.csdn.net"}, {"id": "68bb6fdf9f13", "title": "Host_Drivers | 小陈折腾日记的网盘", "url": "https://alist.geekxw.top/NVIDIA-GRID-Linux-KVM-550.144.02-550.144.03-553.62/Host_Drivers", "description": "来自 alist.geekxw.top 的书签", "category": "NAS", "tags": [], "date_added": "2025-05-17T14:01:17Z", "folder_path": ["书签栏", "Geek", "NAS"], "domain": "alist.geekxw.top"}], "路由": [{"id": "f94869dced85", "title": "immortalwrt-mt798x项目介绍", "url": "https://cmi.hanwckf.top/p/immortalwrt-mt798x/", "description": "来自 cmi.hanwckf.top 的书签", "category": "路由", "tags": [], "date_added": "2023-02-19T19:05:19Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "cmi.hanwckf.top"}, {"id": "580815f77848", "title": "OpenWrt 固件自编译教程：从入门到酸爽！ - 喵斯基部落", "url": "https://www.moewah.com/archives/4003.html", "description": "来自 www.moewah.com 的书签", "category": "路由", "tags": ["教程"], "date_added": "2023-02-19T23:40:59Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.moewah.com"}, {"id": "1dd5a7df41f3", "title": "GitHub - kenzok8/openwrt-packages: openwrt常用软件包", "url": "https://github.com/kenzok8/openwrt-packages", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2023-02-20T16:18:21Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "f92d840eb33e", "title": "零基础编译OpenWrt看这一篇就够了 – 沫延说", "url": "https://blog.topstalk.com/%E9%9B%B6%E5%9F%BA%E7%A1%80%E7%BC%96%E8%AF%91openwrt%E7%9C%8B%E8%BF%99%E4%B8%80%E7%AF%87%E5%B0%B1%E5%A4%9F%E4%BA%86/", "description": "来自 blog.topstalk.com 的书签", "category": "路由", "tags": [], "date_added": "2023-02-22T21:26:00Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "blog.topstalk.com"}, {"id": "5ec737d898fa", "title": "[OpenClash+AdGuardHome]的正确使用姿势/去广告/防污/加速解析-软路由,x86系统,openwrt(x86),Router OS 等-恩山无线论坛 - Powered by Discuz!", "url": "https://www.right.com.cn/forum/thread-4486232-1-1.html", "description": "来自 www.right.com.cn 的书签", "category": "路由", "tags": [], "date_added": "2023-02-23T01:56:15Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.right.com.cn"}, {"id": "397d7e3d858a", "title": "AdGuard Home 用户名与密码是多少-软路由,x86系统,openwrt(x86),Router OS 等-恩山无线论坛 - Powered by Discuz!", "url": "https://www.right.com.cn/FORUM/thread-4037690-1-1.html", "description": "来自 www.right.com.cn 的书签", "category": "路由", "tags": [], "date_added": "2023-02-23T01:56:24Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.right.com.cn"}, {"id": "659fef4ca1ff", "title": "Сайт с рекламой для проверки блокировщиков рекламы: AdBlock, AdBlock Plus, AdGuard, Ghostery…", "url": "https://checkadblock.ru/", "description": "来自 checkadblock.ru 的书签", "category": "路由", "tags": [], "date_added": "2023-02-23T02:07:38Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "checkadblock.ru"}, {"id": "27cb391b1726", "title": "UnblockNeteaseMusic/luci-app-unblockneteasemusic at master", "url": "https://github.com/UnblockNeteaseMusic/luci-app-unblockneteasemusic/tree/master", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub"], "date_added": "2023-02-23T02:13:07Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "01b10ed39353", "title": "MartialBE/luci-app-mosdns: 一个 DNS 转发器 - OpenWRT", "url": "https://github.com/MartialBE/luci-app-mosdns", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub"], "date_added": "2023-02-24T02:59:50Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "c6ce783d2f6a", "title": "<PERSON><PERSON>", "url": "https://opt.cn2qq.com/padavan/", "description": "来自 opt.cn2qq.com 的书签", "category": "路由", "tags": [], "date_added": "2024-02-24T11:05:56Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "opt.cn2qq.com"}, {"id": "fa9145ecf66e", "title": "sagehou/360T7-ImmortalWrt: Custom ImmortalWrt image for 360T7(mt7981)", "url": "https://github.com/sagehou/360T7-ImmortalWrt", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub"], "date_added": "2024-02-28T22:11:19Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "c1b9a87a49ea", "title": "烽火固件全家桶 | CPEManager官方仓库", "url": "http://pan.163wx.cn/%E7%83%BD%E7%81%AB%E5%9B%BA%E4%BB%B6%E5%85%A8%E5%AE%B6%E6%A1%B6", "description": "来自 pan.163wx.cn 的书签", "category": "路由", "tags": [], "date_added": "2024-03-06T20:10:51Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "pan.163wx.cn"}, {"id": "90f1cf2a5ca5", "title": "OpenWrt软路由固件下载与在线定制编译", "url": "https://openwrt.ai/?target=mediatek%2Ffilogic&id=qihoo_360t7", "description": "来自 openwrt.ai 的书签", "category": "路由", "tags": [], "date_added": "2024-08-24T22:57:35Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "openwrt.ai"}, {"id": "20e4ab888ff2", "title": "ImmortalWrt Firmware Selector", "url": "https://firmware-selector.immortalwrt.org/", "description": "来自 firmware-selector.immortalwrt.org 的书签", "category": "路由", "tags": [], "date_added": "2024-08-24T23:47:22Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "firmware-selector.immortalwrt.org"}, {"id": "ed5ee2353c35", "title": "immortalwrt/homeproxy: The modern ImmortalWrt proxy platform for ARM64/AMD64 (powered by sing-box)", "url": "https://github.com/immortalwrt/homeproxy", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub"], "date_added": "2024-08-25T00:56:37Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "b3254142f670", "title": "OpenClash 设置教程 · Aethersailor/Custom_OpenClash_Rules Wiki", "url": "https://github.com/Aethersailor/Custom_OpenClash_Rules/wiki/OpenClash-%E8%AE%BE%E7%BD%AE%E6%95%99%E7%A8%8B", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub", "教程", "Ai"], "date_added": "2024-09-10T01:30:43Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "1892d1720577", "title": "【瑞士军刀】放弃fakeip，拥抱realip，最强网络代理工具sing-box新手指南，从此不知DNS泄漏/DNS污染为何物，软路由插件homeproxy，奈飞DNS解锁、sniff流量嗅探覆写解析 - YouTube", "url": "https://www.youtube.com/watch?v=BAfbkLizFGc&embeds_referring_euri=https%3A%2F%2Fwww.bulianglin.com%2F&source_ve_path=OTY3MTQ", "description": "来自 www.youtube.com 的书签", "category": "路由", "tags": ["工具"], "date_added": "2024-09-11T00:46:48Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.youtube.com"}, {"id": "15150e6aaacc", "title": "OpenWrt 编译步骤与命令详解 - 阿风小子 - 博客园", "url": "https://www.cnblogs.com/kn-zheng/p/17340776.html", "description": "来自 www.cnblogs.com 的书签", "category": "路由", "tags": [], "date_added": "2024-09-13T22:29:38Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.cnblogs.com"}, {"id": "70412b84e981", "title": "对OpenWrt的根分区和系统文件进行扩容 – 你猜的技术分享", "url": "https://www.youguess.site/index.php/2024/01/23/20/28/38/98/", "description": "来自 www.youguess.site 的书签", "category": "路由", "tags": [], "date_added": "2024-09-16T17:47:22Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.youguess.site"}, {"id": "c73cb506be90", "title": "编译教程 | All about X-Wrt", "url": "https://blog.x-wrt.com/docs/build/", "description": "来自 blog.x-wrt.com 的书签", "category": "路由", "tags": ["教程"], "date_added": "2024-10-20T16:18:08Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "blog.x-wrt.com"}, {"id": "d67cee164abe", "title": "校园网防止多设备检测指北 · 瞳のBlog", "url": "https://hetong-re4per.com/posts/multi-device-detection/", "description": "来自 hetong-re4per.com 的书签", "category": "路由", "tags": [], "date_added": "2024-11-05T12:57:07Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "hetong-re4per.com"}, {"id": "b01b1546bff0", "title": "OpenWRT安装WireGuard实现内网穿透 | HomeLab Dolingou", "url": "https://www.dolingou.com/article/openwrt-install-wireguard-for-nat-traversal", "description": "来自 www.dolingou.com 的书签", "category": "路由", "tags": [], "date_added": "2024-12-24T18:34:03Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.dolingou.com"}, {"id": "de0837316d0c", "title": "使用 OpenWrt 24.10.1 官网源码编译固件", "url": "https://maxqiu.com/article/detail/153", "description": "来自 maxqiu.com 的书签", "category": "路由", "tags": [], "date_added": "2025-06-01T14:36:00Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "maxqiu.com"}, {"id": "b3dbc34f528a", "title": "OpenClash 设置方案 · Aethersailor/Custom_OpenClash_Rules Wiki", "url": "https://github.com/Aethersailor/Custom_OpenClash_Rules/wiki/OpenClash-%E8%AE%BE%E7%BD%AE%E6%96%B9%E6%A1%88#01-%E5%85%B3%E4%BA%8E%E6%8E%A8%E8%8D%90%E5%9B%BA%E4%BB%B6", "description": "来自 github.com 的书签", "category": "路由", "tags": ["GitHub", "Ai"], "date_added": "2025-07-17T14:17:16Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "github.com"}, {"id": "ff11053a56b1", "title": "OpenClash 因 GeoIP.dat 启动失败解决办法 - DivineEngine", "url": "https://divineengine.net/article/fix-openclash-geoip-dat-error/", "description": "来自 divineengine.net 的书签", "category": "路由", "tags": [], "date_added": "2025-07-23T00:53:12Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "divineengine.net"}, {"id": "5ac4bb60d6d3", "title": "解决【/tmp/openclash_last_version】下载失败 - 沐沐言 ~ 个人博客", "url": "http://www.jintian.site/index.php/archives/41/", "description": "来自 www.jintian.site 的书签", "category": "路由", "tags": [], "date_added": "2025-07-23T01:23:58Z", "folder_path": ["书签栏", "Geek", "路由"], "domain": "www.jintian.site"}], "技术博客": [{"id": "75a95c820c30", "title": "Rat's Blog - 相逢的人会再相逢", "url": "https://www.moerats.com/", "description": "来自 www.moerats.com 的书签", "category": "技术博客", "tags": [], "date_added": "2022-03-17T00:06:56Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "www.moerats.com"}, {"id": "451d032f8b58", "title": "非常论坛", "url": "https://machbbs.com/", "description": "来自 machbbs.com 的书签", "category": "技术博客", "tags": [], "date_added": "2022-11-20T23:28:54Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "machbbs.com"}, {"id": "e5e497e05105", "title": "FDD（QL-Emotion）Linux搭建Docker版本图文教程", "url": "https://www.luomubiji.host/?p=898", "description": "来自 www.luomubiji.host 的书签", "category": "技术博客", "tags": ["教程"], "date_added": "2022-12-08T23:46:05Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "www.luomubiji.host"}, {"id": "c267a7509a7a", "title": "E5 调用API续订服务：Microsoft 365 E5 Renew X_SundayRX的博客-CSDN博客_e5续订", "url": "https://blog.csdn.net/qq_33212020/article/details/119747634", "description": "来自 blog.csdn.net 的书签", "category": "技术博客", "tags": ["Api"], "date_added": "2023-02-06T01:07:39Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "blog.csdn.net"}, {"id": "65ea1c9b7372", "title": "Windows | 云梦", "url": "https://www.htcp.net/windows", "description": "来自 www.htcp.net 的书签", "category": "技术博客", "tags": [], "date_added": "2023-02-06T01:18:10Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "www.htcp.net"}, {"id": "c741e44c23c7", "title": "搭建Zerotier内网穿透网络及彻底删除zerotier方法_时间就好好的博客-CSDN博客_zerotier删除设备无法添加", "url": "https://blog.csdn.net/jsjason1/article/details/108844943", "description": "来自 blog.csdn.net 的书签", "category": "技术博客", "tags": [], "date_added": "2023-02-07T10:31:53Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "blog.csdn.net"}, {"id": "63bb43022a5b", "title": "win10 你不能访问此共享文件夹，因为你组织的安全策略..._排骨瘦肉丁的博客-CSDN博客_组织的安全策略阻止未经身份验证的来宾访问", "url": "https://blog.csdn.net/iamlihongwei/article/details/79377657", "description": "来自 blog.csdn.net 的书签", "category": "技术博客", "tags": [], "date_added": "2023-02-10T18:39:22Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "blog.csdn.net"}, {"id": "478ac1ebe65c", "title": "AutoBangumi：自动追番，解放双手 - 初之音", "url": "https://www.himiku.com/archives/auto-bangumi.html", "description": "来自 www.himiku.com 的书签", "category": "技术博客", "tags": [], "date_added": "2023-02-13T12:05:34Z", "folder_path": ["书签栏", "Geek", "技术博客"], "domain": "www.himiku.com"}], "AI": [{"id": "11721f246772", "title": "Civitai", "url": "https://civitai.com/", "description": "来自 civitai.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2023-02-25T14:18:37Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "civitai.com"}, {"id": "b03693c63d04", "title": "AutoDL-品质GPU租用平台-租GPU就上AutoDL", "url": "https://www.autodl.com/console/instance/list", "description": "来自 www.autodl.com 的书签", "category": "AI", "tags": [], "date_added": "2023-03-10T17:32:56Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.autodl.com"}, {"id": "6bd50a728805", "title": "CUDA Toolkit Archive | NVIDIA Developer", "url": "https://developer.nvidia.com/cuda-toolkit-archive", "description": "来自 developer.nvidia.com 的书签", "category": "AI", "tags": [], "date_added": "2023-03-26T00:31:45Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "developer.nvidia.com"}, {"id": "3fae0f94ccff", "title": "在Windows下正确地编译最新的pytorch和tensorflow_编译pytorch_纯洁的小火车的博客-CSDN博客", "url": "https://blog.csdn.net/weixin_42122722/article/details/122374308", "description": "来自 blog.csdn.net 的书签", "category": "AI", "tags": [], "date_added": "2023-05-10T01:59:42Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "blog.csdn.net"}, {"id": "cd1f8abf9d4b", "title": "AI导航 - 优设AI导航 - 专业AIGC网站导航", "url": "https://hao.uisdc.com/ai/", "description": "来自 hao.uisdc.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2024-02-04T23:57:05Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "hao.uisdc.com"}, {"id": "82703674e44f", "title": "ddean2009/MoneyPrinterPlus: AI一键批量生成各类短视频,自动批量混剪短视频,自动把视频发布到抖音,快手,小红书,视频号上,赚钱从来没有这么容易过! 支持本地语音模型chatTTS,fasterwhisper,GPTSoVITS,支持云语音：Azure,阿里云,腾讯云。支持Stable diffusion,comfyUI直接AI生图。Generate short videos with one click using AI LLM,print money together! support:chatTTS,faster-whisper,GPTSoVITS,Azure,tencent Cloud,Ali Cloud.", "url": "https://github.com/ddean2009/MoneyPrinterPlus?tab=readme-ov-file#%E5%89%8D%E6%8F%90%E6%9D%A1%E4%BB%B6", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2024-10-04T02:31:10Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "54081aa2fca0", "title": "WatermarkRemover/ at master · lxulxu/WatermarkRemover", "url": "https://github.com/lxulxu/WatermarkRemover/tree/master", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2024-10-04T02:51:32Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "62b40653dfb8", "title": "Gemini 2.0 Flash Multimodal Live API Client", "url": "https://jokero-gem.deno.dev/", "description": "来自 jokero-gem.deno.dev 的书签", "category": "AI", "tags": ["Api"], "date_added": "2025-01-11T11:57:33Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "jokero-gem.deno.dev"}, {"id": "3502929b3cfa", "title": "CodeWithGPU | 能复现才是好算法", "url": "https://www.codewithgpu.com/i/issuedetail/Akegarasu/lora-scripts/kohya-ss_flux/172635524838/0", "description": "来自 www.codewithgpu.com 的书签", "category": "AI", "tags": [], "date_added": "2025-01-16T02:09:43Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.codewithgpu.com"}, {"id": "d78181c03283", "title": "Calcium-Ion/new-api: AI模型接口管理与分发系统，支持将多种大模型转为OpenAI格式调用、支持Midjourney Proxy、<PERSON><PERSON>、<PERSON>rank，兼容易支付协议，可供个人或者企业内部管理与分发渠道使用，本项目基于One API二次开发。🍥 The next-generation LLM gateway and AI asset management system supports multiple languages.", "url": "https://github.com/Calcium-Ion/new-api", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai", "Api"], "date_added": "2025-01-18T21:58:02Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "8fc8419819fc", "title": "做了个最快的B站直播录制、自动切片、自动渲染弹幕和字幕并投稿的项目，兼容 10 年前的机器！欢迎各位佬友使用并提建议。 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/325246/9", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-01-20T20:14:24Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "5fcf98e8760d", "title": "让自动获取免费机场订阅更自动 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/93330", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-03-06T07:27:59Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "a9daec5cd937", "title": "新版宝宝教程 - Google 文档", "url": "https://docs.google.com/document/d/1AbhYWoEpgPg0PilaGIhKphdMOzhl_SVtaDhWlacH_Tg/edit?pli=1&tab=t.0#heading=h.g4xn5w7pauzg", "description": "来自 docs.google.com 的书签", "category": "AI", "tags": ["教程"], "date_added": "2025-03-26T23:50:44Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "docs.google.com"}, {"id": "205794d84059", "title": "做了一个自动切片的小工具，欢迎各位佬无门槛当上切片员，也欢迎提建议！ - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/515179/13", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "工具"], "date_added": "2025-03-27T13:28:19Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "81693d94ba9f", "title": "【Cursor】可视化启动注册任务及账号管理 - 福利羊毛 / 福利羊毛, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/513320", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-03-27T13:34:30Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "cfca4cb51856", "title": "facebookresearch/xformers: Hackable and optimized Transformers building blocks, supporting a composable construction.", "url": "https://github.com/facebookresearch/xformers?tab=readme-ov-file", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-03-29T15:10:04Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "e30659e311b0", "title": "Tutorial (setup): Train Flux.1 Dev LoRAs using \"ComfyUI Flux Trainer\" : r/StableDiffusion", "url": "https://www.reddit.com/r/StableDiffusion/comments/1f5onyx/tutorial_setup_train_flux1_dev_loras_using/", "description": "来自 www.reddit.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2025-03-30T21:19:57Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.reddit.com"}, {"id": "869c9a492ea9", "title": "EvilBT/ComfyUI_SLK_joy_caption_two: ComfyUI Node", "url": "https://github.com/EvilBT/ComfyUI_SLK_joy_caption_two?tab=readme-ov-file", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-03-30T21:52:54Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "c4318cbab495", "title": "ComfyUI_SLK_joy_caption_two/examples/workflows.png at main · EvilBT/ComfyUI_SLK_joy_caption_two", "url": "https://github.com/EvilBT/ComfyUI_SLK_joy_caption_two/blob/main/examples/workflows.png", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-03-30T22:44:36Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "33f325aa8ccd", "title": "探索MCP-我的学习与实践笔记 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/504520", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-04-01T18:54:23Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "df6c0250b36d", "title": "【4/01 Apifox Mcp】个人使用 Cursor 一些调优和经验 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/523255/11", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "Api"], "date_added": "2025-04-01T19:14:13Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "c31295153368", "title": "awesome-mcp-servers/README-zh.md at main · punkpeye/awesome-mcp-servers", "url": "https://github.com/punkpeye/awesome-mcp-servers/blob/main/README-zh.md", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-01T22:02:32Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "3a25f5280286", "title": "Smithery - Model Context Protocol Registry", "url": "https://smithery.ai/?q=tiktok", "description": "来自 smithery.ai 的书签", "category": "AI", "tags": [], "date_added": "2025-04-02T01:35:01Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "smithery.ai"}, {"id": "e29026edddff", "title": "zhangyiming748/coze: 测试这个新的AI智能体", "url": "https://github.com/zhangyiming748/coze", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-02T01:56:11Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "5a651cccf5e4", "title": "zhtyyx/ioe: One-Stop Retail Inventory Solution", "url": "https://github.com/zhtyyx/ioe", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-02T08:11:40Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "465e10b04c9b", "title": "xiaoti12/autodl_bot", "url": "https://github.com/xiaoti12/autodl_bot", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-04-02T08:18:53Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "ca69c6923b5d", "title": "ioe/README.docker_zh.md at main · zhtyyx/ioe", "url": "https://github.com/zhtyyx/ioe/blob/main/README.docker_zh.md", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-02T18:24:17Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "a65690e5edf7", "title": "首页 | 类脑智识库 · ΓΝΩΣΗ WIKI", "url": "https://wiki.xn--35zx7g.org/", "description": "来自 wiki.xn--35zx7g.org 的书签", "category": "AI", "tags": [], "date_added": "2025-04-04T02:05:24Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "wiki.xn--35zx7g.org"}, {"id": "9d5c9d01a650", "title": "Apps - miludeerforest | Modal", "url": "https://modal.com/apps/miludeerforest/main", "description": "来自 modal.com 的书签", "category": "AI", "tags": [], "date_added": "2025-04-04T18:46:14Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "modal.com"}, {"id": "e63ccbec9381", "title": "How to Train Multiple Flux LoRA Automatically on Free H100 GPUs with Just One Click - YouTube", "url": "https://www.youtube.com/watch?v=Xjuz92Xmv5w", "description": "来自 www.youtube.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2025-04-04T18:58:57Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.youtube.com"}, {"id": "c184c5195299", "title": "AINxtGen/ai-toolkit", "url": "https://github.com/AINxtGen/ai-toolkit", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-04T19:07:15Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "7f605ee95656", "title": "在 Free H100 GPU 上训练多个 Flux LoRA : r/comfyui --- Train Multiple Flux LoRA on Free H100 GPUs : r/comfyui", "url": "https://www.reddit.com/r/comfyui/comments/1ibgbvu/train_multiple_flux_lora_on_free_h100_gpus/", "description": "来自 www.reddit.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2025-04-04T19:22:01Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.reddit.com"}, {"id": "229434e459d6", "title": "AINxtGen/ai-toolkit", "url": "https://github.com/AINxtGen/ai-toolkit?tab=readme-ov-file", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-04T19:23:09Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "dcfd2a05c920", "title": "Ai动画77-乱入！多图融合视频一致性再突破！Wan2.1 Skyreels-A2预览版！Vace出现强大竞争对手！通义万象生态又出强大模型-Comfyui教程 - YouTube", "url": "https://www.youtube.com/watch?v=UvddiyCAf2k", "description": "来自 www.youtube.com 的书签", "category": "AI", "tags": ["教程", "Ai"], "date_added": "2025-04-06T22:55:51Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.youtube.com"}, {"id": "04f8fb56d2a5", "title": "开源自荐|一个简洁、美观、实用的提示词管理网站。 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/282700", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "开源"], "date_added": "2025-04-07T12:48:40Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "82995edad292", "title": "SuperSMSBridge：基于 Telegram 超级群组的短信转发中间件 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/537557/4", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-04-07T12:54:04Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "4e3dc3e68a81", "title": "bestK/email_worker_parser: A simple fake email api", "url": "https://github.com/bestK/email_worker_parser", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai", "Api"], "date_added": "2025-04-08T19:17:01Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "96821a56476b", "title": "clash脚本：增加了cursor相关的规则 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/542912/2", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-04-09T12:19:25Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "a1550a832840", "title": "Cursor 无限邮箱账号注册 - 开发调优 / 开发调优, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/541154", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-04-09T13:19:11Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "670135cabf23", "title": "轻量级的节点订阅转换工具：sublink-worker，支持 Sing-Box、Clash - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/193549", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "工具"], "date_added": "2025-04-09T16:42:47Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "06d35e43812e", "title": "学生包全能油猴脚本[自动定位 + 虚拟摄像头] - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/177723", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-04-10T00:50:52Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "0d4bf316416f", "title": "fff122/PicSpider: 批量爬取写真站,并展示", "url": "https://github.com/fff122/PicSpider", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-04-13T18:57:34Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "07400e7c0277", "title": "codelf/README_CN.md at main · Disdjj/codelf", "url": "https://github.com/Disdjj/codelf/blob/main/README_CN.md", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-14T01:04:23Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "72d4a56be042", "title": "yeongpin/cursor-free-vip: [Support 0.48.x]（Reset Cursor AI MachineID & Auto Sign Up / In & Bypass Higher Token Limit）自动注册 Cursor Ai ，自动重置机器ID ， 免费升级使用Pro功能: You've reached your trial request limit. / Too many free trial accounts used on this machine. Please upgrade to pro. We have this limit in place to prevent abuse. Please let us know if you believe this is a mistake.", "url": "https://github.com/yeongpin/cursor-free-vip", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-04-16T10:26:46Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "3d06e2ef920f", "title": "PoolHub", "url": "https://poolhub.me/", "description": "来自 poolhub.me 的书签", "category": "AI", "tags": [], "date_added": "2025-04-26T01:10:11Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "poolhub.me"}, {"id": "305c20d21c8c", "title": "简单易懂的现代魔法 - n8n 中文使用教程 | 页面找不到啦", "url": "https://n8n.akashio.com/welcome", "description": "来自 n8n.akashio.com 的书签", "category": "AI", "tags": ["教程"], "date_added": "2025-04-29T14:33:54Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "n8n.akashio.com"}, {"id": "6d0c8abf45c8", "title": "【Cursor】Cursor 全局通用规则Rules V4.5 ：多维思考+五大规则模式 让你码到飞起 - 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/536898", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-05-03T14:55:38Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "8cc71cc1fe78", "title": "学生证生成器 (仅供内部测试使用)", "url": "https://student-id.pages.dev/", "description": "来自 student-id.pages.dev 的书签", "category": "AI", "tags": [], "date_added": "2025-05-07T17:37:12Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "student-id.pages.dev"}, {"id": "eb4900f827ff", "title": "TikTok Video Downloader | Download TikTok video without watermark - TikVid", "url": "https://tikvid.io/en", "description": "来自 tikvid.io 的书签", "category": "AI", "tags": ["Tiktok"], "date_added": "2025-05-09T11:59:33Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "tikvid.io"}, {"id": "1b437843a0a9", "title": "VYNCX/F5-TTS-THAI: Text-to-Speech (TTS) 泰语 — 使用 Flow Matching 技术从文本生成语音的工具 --- VYNCX/F5-TTS-THAI: Text-to-Speech (TTS) ภาษาไทย — เครื่องมือสร้างเสียงพูดจากข้อความด้วยเทคนิค Flow Matching", "url": "https://github.com/VYNCX/F5-TTS-THAI", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai", "工具"], "date_added": "2025-05-10T09:45:01Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "5ff6e1df64d6", "title": "飞书云文档", "url": "https://jexopm4t2a.feishu.cn/wiki/EjRPwux9DiNUtakOd1BcLQWEn3f", "description": "来自 jexopm4t2a.feishu.cn 的书签", "category": "AI", "tags": [], "date_added": "2025-05-10T17:12:57Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "jexopm4t2a.feishu.cn"}, {"id": "8c1262aa6306", "title": "redkaytop/framepack_comfyui · Cloud Native Build", "url": "https://cnb.cool/redkaytop/framepack_comfyui", "description": "来自 cnb.cool 的书签", "category": "AI", "tags": [], "date_added": "2025-05-12T00:47:16Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "cnb.cool"}, {"id": "e24bc1691d43", "title": "VIZINTZOR/F5-TTS-THAI · Hugging Face", "url": "https://huggingface.co/VIZINTZOR/F5-TTS-THAI", "description": "来自 huggingface.co 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2025-05-22T11:17:30Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "huggingface.co"}, {"id": "d3e3054f6a4f", "title": "Custom_OpenClash_Rules/cfg at main · Aethersailor/Custom_OpenClash_Rules", "url": "https://github.com/Aethersailor/Custom_OpenClash_Rules/tree/main/cfg", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-05-24T15:44:13Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "b9b2c9cc0f9e", "title": "无意间发现了一个AI视频总结的工具/项目 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/678756", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "工具", "Ai"], "date_added": "2025-05-26T08:51:47Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "e425eef07f63", "title": "Cursor 软件开发 MCP工具组合推荐 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/681924/2", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "工具"], "date_added": "2025-05-29T10:30:49Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "f2e6c383de37", "title": "下一个SJSU，抓紧时间 - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/680339/384", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-05-29T15:22:23Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "4dad24a0e650", "title": "记录一下申请ccc学校具体流程，暂未下号，正在申请中！ - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/686360", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-05-29T16:58:37Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "3e0f08962ddc", "title": "Gemini2API 服务端实现，支持 RooCode（佬友共同维护版本） - 开发调优 / 开发调优, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/575699", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "Api"], "date_added": "2025-06-02T22:51:55Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "4f2279058ab6", "title": "MCP Feedback Enhanced 支持ssh remote、WSL，Cursor、Augment、Trae、Widsurf 等使用次数加倍 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/701931/21", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-06-05T13:16:12Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "43410a2d610a", "title": "Cloudflare 临时邮件", "url": "https://mail.qiumail.ink/", "description": "来自 mail.qiumail.ink 的书签", "category": "AI", "tags": [], "date_added": "2025-02-03T18:17:14Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "mail.qiumail.ink"}, {"id": "d6d20ae76cca", "title": "注册美国区Apple ID保姆级教程 - 知乎", "url": "https://zhuanlan.zhihu.com/p/623576755", "description": "来自 zhuanlan.zhihu.com 的书签", "category": "AI", "tags": ["教程"], "date_added": "2025-06-05T16:39:59Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "zhuanlan.zhihu.com"}, {"id": "e2add12e1514", "title": "jeromeleong/poe2openai: Convert Poe API into OpenAI format API", "url": "https://github.com/jeromeleong/poe2openai", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai", "Api"], "date_added": "2025-06-06T11:31:27Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "7fe671c13f96", "title": "dbccccccc/ttsfm：TTSFM 是一个逆向工程的 API 服务器，它镜像 OpenAI 的 TTS 服务，为具有多种语音选项的文本到语音转换提供兼容的接口。 --- dbccccccc/ttsfm: TTSFM is a reverse-engineered API server that mirrors OpenAI's TTS service, providing a compatible interface for text-to-speech conversion with multiple voice options.", "url": "https://github.com/dbccccccc/ttsfm", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai", "Api"], "date_added": "2025-06-06T15:13:17Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "d4b320ac4cd9", "title": "gordon123/ComfyUI-F5-TTS-EN：Efit TTS 的 ComfyUI 节点泰语很清楚，你可以试试。 --- gordon123/ComfyUI-F5-TTS-TH: ComfyUI node สำหรับ เอฟไฟท์ทีทีเอส ภาษาไทย ชัดแจ๋ว ทดลองดูได้ครับ", "url": "https://github.com/gordon123/ComfyUI-F5-TTS-TH", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-06-07T01:13:03Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "e8cce81f17db", "title": "用AI写了一个套图爬虫 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/711707/3", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "Ai"], "date_added": "2025-06-10T18:56:43Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "33fd60f012b3", "title": "OpenList 交互式管理脚本 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/724976", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-06-16T08:30:49Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "f537b8bb32c9", "title": "Home · ChefKissInc/QEMUAppleSilicon Wiki · GitHub", "url": "https://github.com/ChefKissInc/QEMUAppleSilicon/wiki", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2025-06-16T12:38:54Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "9d84edacc629", "title": "一叶轻舟", "url": "https://**************/", "description": "来自 ************** 的书签", "category": "AI", "tags": [], "date_added": "2025-06-20T01:41:32Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "**************"}, {"id": "3d4f92866627", "title": "收件箱 - miludeerforest", "url": "https://email-web-app.j3.workers.dev/", "description": "来自 email-web-app.j3.workers.dev 的书签", "category": "AI", "tags": [], "date_added": "2025-06-20T14:09:20Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "email-web-app.j3.workers.dev"}, {"id": "817eba541153", "title": "Home | ElevenLabs", "url": "https://elevenlabs.io/app/home", "description": "来自 elevenlabs.io 的书签", "category": "AI", "tags": [], "date_added": "2025-06-23T11:47:55Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "elevenlabs.io"}, {"id": "b4a5cd8133f1", "title": "border1px/JianYingProDraft", "url": "https://github.com/border1px/JianYingProDraft", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-06-27T10:41:30Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "c8df5e59b998", "title": "【长期更新】Augment Agent 工具集标准化工作流提示词模板 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/693762", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "工具"], "date_added": "2025-06-27T18:46:56Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "8c5a94d7298d", "title": "GuanYixuan/pyJianYingDraft: 轻量、灵活、易上手的Python剪映草稿生成及导出工具，构建全自动化视频剪辑/混剪流水线", "url": "https://github.com/GuanYixuan/pyJianYingDraft?tab=readme-ov-file#%E5%85%B3%E9%94%AE%E5%B8%A7", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "工具"], "date_added": "2025-06-28T17:22:13Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "53d58c03b3e5", "title": "v0 API 教程 - 福利羊毛 / 福利羊毛, Lv3 - LINUX DO", "url": "https://linux.do/t/topic/755343", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "教程", "Api"], "date_added": "2025-06-29T20:51:49Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "91de2211cfb3", "title": "实现claude code自由！一键部署的Docker化claude-code-proxy解决方案 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/760680", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-02T23:46:01Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "ef74ac6f5fb2", "title": "将 n8n 简单汉化了一版，顺便启用了企业版 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/745531/36", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-07T10:55:54Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "3f5207f5e759", "title": "Any Router", "url": "https://anyrouter.top/console/token", "description": "来自 anyrouter.top 的书签", "category": "AI", "tags": [], "date_added": "2025-07-10T10:55:09Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "anyrouter.top"}, {"id": "264df6213580", "title": "项目探索 - Linux Do CDK", "url": "https://cdk.linux.do/explore", "description": "来自 cdk.linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-10T17:22:32Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "cdk.linux.do"}, {"id": "5f419192d4a5", "title": "高级模型无限使用策略，可2api, 无预制提示词 - 福利羊毛 / 福利羊毛, Lv3 - LINUX DO", "url": "https://linux.do/t/topic/782855", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux", "Api"], "date_added": "2025-07-14T08:53:23Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "38780613a2c1", "title": "AICode/README_CN.md at main · sylearn/AICode", "url": "https://github.com/sylearn/AICode/blob/main/README_CN.md", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai"], "date_added": "2025-07-17T17:40:17Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "a1b92132b335", "title": "【github 开源】零成本1分钟部署，告别信息过载的多平台热点聚合工具(手机推送)，v2.0.0 刚更新(支持docker）快破1000⭐了 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/797132", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["开源", "Linux", "工具", "<PERSON><PERSON><PERSON>"], "date_added": "2025-07-18T08:45:59Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "a06bd641e7fc", "title": "Kontext-DEV【先去除后替换产品】换场景 - RunningHub ComfyUI Workflow", "url": "https://www.runninghub.cn/post/1945660410626285569", "description": "来自 www.runninghub.cn 的书签", "category": "AI", "tags": [], "date_added": "2025-07-18T09:43:11Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.runninghub.cn"}, {"id": "9be35259eec4", "title": "Cursor/Augment Code开发Rules - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/780085", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-18T16:35:16Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "0be2ae7404ca", "title": "⚡ N8N Workflow Documentation", "url": "https://n8n-workflows-production-9cc4.up.railway.app/", "description": "来自 n8n-workflows-production-9cc4.up.railway.app 的书签", "category": "AI", "tags": [], "date_added": "2025-07-22T16:17:44Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "n8n-workflows-production-9cc4.up.railway.app"}, {"id": "89d7b9dba472", "title": "【T佬】GPT-Load 1.0正式版发布，支持多渠道Key池轮询代理服务！ - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/789409", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-23T16:24:19Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "3efd0b9832d3", "title": "TSCarterJr/UnsecuredAPIKeys-OpenSource: The code base behind the [Former] UnsecuredAPIKeys.com", "url": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Api"], "date_added": "2025-07-24T08:47:27Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "0a0611e59319", "title": "Augment 又送福利了 - 搞七捻三 / 搞七捻三, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/813755", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-25T08:27:11Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "619fba8aa08b", "title": "WIPDF", "url": "https://wipdf.vercel.app/", "description": "来自 wipdf.vercel.app 的书签", "category": "AI", "tags": [], "date_added": "2025-07-25T09:03:09Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "wipdf.vercel.app"}, {"id": "6d4ac6bec2a2", "title": "yuaotian/go-augment-cleaner: 清理Augment缓存和生成设备SessionId/解决 VSCode、Cursor、JetBrains 系列 IDE 中 Augment 插件无法登录的问题（Sign in failed. If you have a firewall, please add）/将所有 d1-d20.api.augmentcode.com 域名统一指向延迟最低的服务器IP", "url": "https://github.com/yuaotian/go-augment-cleaner", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "Ai", "Api"], "date_added": "2025-07-25T10:31:51Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "95d191af36fa", "title": "Gak<PERSON>No<PERSON><PERSON>/hajimi-king: 人人都是哈基米大王", "url": "https://github.com/GakkiNoOne/hajimi-king", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub"], "date_added": "2025-07-26T10:45:59Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "b0eea24f49a6", "title": "〖⭐️ (Pro Max) LINUX DO二级及以上用户尊享顶级阅览室〗盘点那些评论量多的帖子 (目前整理的话题总评论量已超48000条,升3级和3级保级必备神器,已达成预期目标,停更) - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/329593", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-28T08:48:18Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "c9379dc42bff", "title": "不需要手动算倍率！最全的倍率集合 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/819208/8", "description": "来自 linux.do 的书签", "category": "AI", "tags": ["Linux"], "date_added": "2025-07-28T08:51:34Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "linux.do"}, {"id": "fc523c8a6321", "title": "tsxcw/mtab: Mtab书签导航程序 - 免费无广告的浏览器书签助手，多端同步、美观易用的在 线导航和书签工具，自主研发免费使用，帮助您高效管理 网页和应用，提升在线体验。", "url": "https://github.com/tsxcw/mtab", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "工具"], "date_added": "2025-07-28T08:55:46Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "a87f79abf13d", "title": "Google 翻译", "url": "https://translate.google.com/?hl=zh-CN", "description": "来自 translate.google.com 的书签", "category": "AI", "tags": [], "date_added": "2022-11-06T13:25:04Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "translate.google.com"}, {"id": "1c8bdb838ed2", "title": "0086-19076089329", "url": "https://po.givemestar.com/dashboard", "description": "来自 po.givemestar.com 的书签", "category": "AI", "tags": [], "date_added": "2025-01-17T14:14:19Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "po.givemestar.com"}, {"id": "6ae2a11d93a9", "title": "BigSeller - Orders", "url": "https://www.bigseller.pro/web/order/index.htm?status=new", "description": "来自 www.bigseller.pro 的书签", "category": "AI", "tags": [], "date_added": "2024-12-11T10:19:20Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.bigseller.pro"}, {"id": "e7c52ccefbf9", "title": "FastMoss-挖掘TikTok热销爆品、解析爆品打造案例", "url": "https://www.fastmoss.com/zh/e-commerce/search?page=1&l1_cid=16&l2_cid=909192&l3_cid=911752®ion=TH", "description": "来自 www.fastmoss.com 的书签", "category": "AI", "tags": ["Tiktok"], "date_added": "2025-06-09T09:13:47Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.fastmoss.com"}, {"id": "9632a4807c18", "title": "DuckDuckGo Email | Email Protection from DuckDuckGo", "url": "https://duckduckgo.com/email/settings/autofill", "description": "来自 duckduckgo.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2025-06-23T09:23:50Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "duckduckgo.com"}, {"id": "82d4ebdc6671", "title": "Build | Google AI Studio", "url": "https://aistudio.google.com/app/apps/drive/169U2Al5556WX7bcWYdaPwHvzoAU7PqW_", "description": "来自 aistudio.google.com 的书签", "category": "AI", "tags": ["Ai"], "date_added": "2025-07-18T15:31:06Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "aistudio.google.com"}, {"id": "e79c0fec17c3", "title": "tcp ************:80", "url": "https://tcp.ping.pe/************:80", "description": "来自 tcp.ping.pe 的书签", "category": "AI", "tags": [], "date_added": "2023-11-04T13:01:50Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "tcp.ping.pe"}, {"id": "934bdaeb9073", "title": "X-WRT/OpenWrt/LEDE 固件下载", "url": "https://downloads.x-wrt.com/rom/", "description": "来自 downloads.x-wrt.com 的书签", "category": "AI", "tags": [], "date_added": "2023-12-15T00:32:23Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "downloads.x-wrt.com"}, {"id": "c26acaff0e4d", "title": "金山文档 | WPS云文档", "url": "https://www.kdocs.cn/view/l/caSWWxarIWrZ", "description": "来自 www.kdocs.cn 的书签", "category": "AI", "tags": [], "date_added": "2024-04-05T13:12:34Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.kdocs.cn"}, {"id": "7e6c6b113f2c", "title": "跨境电商职场人发声平台，跨境黑名单，避坑表 - 若比邻网", "url": "https://www.ratecompany.org/", "description": "来自 www.ratecompany.org 的书签", "category": "AI", "tags": ["电商"], "date_added": "2022-02-28T02:28:16Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.ratecompany.org"}, {"id": "8145de8f8ae2", "title": "USTC IP Blacklist", "url": "https://blackip.ustc.edu.cn/intro.php", "description": "来自 blackip.ustc.edu.cn 的书签", "category": "AI", "tags": [], "date_added": "2023-02-01T00:35:05Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "blackip.ustc.edu.cn"}, {"id": "0f7f83f2afa1", "title": "2023年免费国内CDN有哪些?国内免费CDN小结 | SKY博客", "url": "https://www.sky350.com/1235.html", "description": "来自 www.sky350.com 的书签", "category": "AI", "tags": [], "date_added": "2023-02-03T02:48:48Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.sky350.com"}, {"id": "9ce842787be9", "title": "Joker_WRT - 概况 - LuCI", "url": "http://*************:20080/cgi-bin/luci/", "description": "来自 *************:20080 的书签", "category": "AI", "tags": [], "date_added": "2023-02-06T04:17:25Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "*************:20080"}, {"id": "d548cb01512a", "title": "柚坛社区", "url": "https://www.uotan.cn/", "description": "来自 www.uotan.cn 的书签", "category": "AI", "tags": [], "date_added": "2024-05-17T12:51:59Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "www.uotan.cn"}, {"id": "8ba27289f9de", "title": "GitHub - bepass-org/oblivion-desktop: Oblivion Desktop - Unofficial Warp Client for Windows/Mac/Linux", "url": "https://github.com/bepass-org/oblivion-desktop", "description": "来自 github.com 的书签", "category": "AI", "tags": ["GitHub", "<PERSON><PERSON><PERSON>"], "date_added": "2024-06-08T07:37:05Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "github.com"}, {"id": "cae685f66f01", "title": "如何将G<PERSON><PERSON><PERSON><PERSON> sim卡转换为esim | <PERSON>'s Blog", "url": "https://azhu.site/posts/1015/", "description": "来自 azhu.site 的书签", "category": "AI", "tags": [], "date_added": "2024-08-16T19:50:52Z", "folder_path": ["书签栏", "Geek", "AI"], "domain": "azhu.site"}]}}