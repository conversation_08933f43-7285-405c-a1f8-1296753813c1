#!/usr/bin/env python3
"""
调试Chrome书签HTML解析器
"""

from bs4 import BeautifulSoup

def debug_html_structure(file_path):
    """调试HTML结构"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    print("=== HTML结构分析 ===")
    print(f"文档标题: {soup.title.string if soup.title else 'None'}")
    
    # 查找所有DL元素
    dl_elements = soup.find_all('dl')
    print(f"找到 {len(dl_elements)} 个DL元素")
    
    # 查找所有DT元素
    dt_elements = soup.find_all('dt')
    print(f"找到 {len(dt_elements)} 个DT元素")
    
    # 查找所有A标签
    a_elements = soup.find_all('a')
    print(f"找到 {len(a_elements)} 个A标签")
    
    # 查找所有H3标签
    h3_elements = soup.find_all('h3')
    print(f"找到 {len(h3_elements)} 个H3标签")
    
    print("\n=== 前10个A标签示例 ===")
    for i, a in enumerate(a_elements[:10]):
        href = a.get('href', 'No HREF')
        text = a.get_text().strip()
        print(f"{i+1}. {text[:50]}... -> {href[:50]}...")
    
    print("\n=== 前5个H3标签示例 ===")
    for i, h3 in enumerate(h3_elements[:5]):
        text = h3.get_text().strip()
        print(f"{i+1}. {text}")
    
    # 分析第一个DL的结构
    if dl_elements:
        print("\n=== 第一个DL元素的直接子元素 ===")
        first_dl = dl_elements[0]
        for i, child in enumerate(first_dl.children):
            if hasattr(child, 'name') and child.name:
                print(f"{i+1}. {child.name}")
                if child.name == 'dt':
                    # 检查DT的内容
                    h3 = child.find('h3')
                    a = child.find('a')
                    if h3:
                        print(f"   -> H3: {h3.get_text().strip()}")
                    if a:
                        print(f"   -> A: {a.get_text().strip()[:30]}...")

if __name__ == '__main__':
    debug_html_structure('bookmarks_2025_7_28.html')
