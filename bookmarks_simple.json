{"bookmarks": [{"title": "FastMoss-TikTok短视频&直播电商与达人营销数据分析 - FastMoss数据,有乐今天", "url": "https://www.fastmoss.com/zh/e-commerce/search", "category": "Tiktok", "tags": ["Tiktok", "电商"]}, {"title": "搜索 TikTok 商品", "url": "https://didadog.com/libraryProduct/search", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "TikTok Business Center", "url": "https://business.tiktok.com/manage/overview?org_id=7392109665901068289&detail_adv=0&filters=3,1,2,4,5&selectAccountType=1", "category": "Tiktok", "tags": ["Tiktok", "TikTok"]}, {"title": "TikTok Creator Marketplace", "url": "https://creatormarketplace.tiktok.com/ad/dashboard", "category": "Tiktok", "tags": ["Tiktok", "TikTok"]}, {"title": "主页 - 美容时尚", "url": "https://beautystyles.ch/", "category": "Tiktok", "tags": []}, {"title": "一文看懂TikTok Shop全托管模式 - TikTok Shop全托管卖家中心学习平台", "url": "https://learning-center.fanczs.com/support/content/138103?graphId=654&hideFooter=1&hideUnify=1&mappingType=2&pageId=441&spaceId=235×tamp=*************", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "TK导航 - ImTiktoker 玩家网", "url": "https://imtiktoker.com/", "category": "Tiktok", "tags": ["Tiktok", "TikTok"]}, {"title": "全部商品页", "url": "https://shop77l302963o247.1688.com/page/offerlist.htm?spm=a2615.********.wp_pc_common_topnav.0", "category": "Tiktok", "tags": []}, {"title": "‬﻿⁣﻿⁢⁣‍⁣⁣​⁤​⁡​‬​⁡⁣‌‍‬‬​‌⁤⁢‬⁣‍​‬⁤⁡⁤​﻿⁡⁣﻿‬⁣‬‌⁡​​⁣‌商家建联达人合作指南 - Feishu Docs", "url": "https://w6l1a2bce9.feishu.cn/docx/F8Fkd7bpioatsZx9ztrc1w57nad", "category": "Tiktok", "tags": []}, {"title": "话术管理", "url": "https://www.feiyudaren.com/chatTmp", "category": "Tiktok", "tags": []}, {"title": "免费开源神器：一键分发，自动化短视频上传，支持主流个自媒体平台，矩阵化运营 - 掘金", "url": "https://juejin.cn/post/7372114027840208911", "category": "Tiktok", "tags": ["开源"]}, {"title": "TikTok Creator Marketplace", "url": "https://creatormarketplace.tiktok.com/ad/market", "category": "Tiktok", "tags": ["Tiktok", "TikTok"]}, {"title": "TikTok数据分析（TikTok Analytics）终极使用指南 | tk0123跨境电商导航", "url": "https://www.tk0123.com/444.html", "category": "Tiktok", "tags": ["Tiktok", "电商"]}, {"title": "Tiktok 广告投放花了 150 万 RMB 得出来的经验 - 宇周博客", "url": "https://yuzhoublog.com/tiktok-advertising/", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "美国tiktok千粉号购买-tiktok美国满月千粉白号批发-TikTok千粉账号购买平台", "url": "https://www.tiktokfensi.com/tiktokqianfen/meiguo/", "category": "Tiktok", "tags": ["Tiktok", "TikTok"]}, {"title": "jewvfRf01/TikTok-viewbot: 🔥 tiktok viewbot 500+ per second 🔥 tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot tiktok view bot views bot tiktok viewbot ztepgr", "url": "https://github.com/jewvfRf01/TikTok-viewbot", "category": "Tiktok", "tags": ["Tiktok", "GitHub"]}, {"title": "TikTokMod - OpenDesktop.org", "url": "https://www.opendesktop.org/p/1515346/", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "【Tiktok分享】商品卡截流玩法，对应资源体系搭建 - 知无不言跨境电商社区", "url": "https://www.wearesellers.com/question/85838", "category": "Tiktok", "tags": ["Tiktok", "电商"]}, {"title": "刚刚【social-auto-upload】更新【快手】的支持：免费开源自媒体视频一键分发，自动化上传 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/173129", "category": "Tiktok", "tags": ["开源", "Linux"]}, {"title": "National Drug Code Directory", "url": "https://dps.fda.gov/ndc/searchresult?selection=finished_product&content=PROPRIETARYNAME&type=SADOER+", "category": "Tiktok", "tags": []}, {"title": "产品 - OMS", "url": "https://oms-cntodd.xlwms.com/product/list", "category": "Tiktok", "tags": []}, {"title": "培训资料", "url": "https://drive.weixin.qq.com/s?k=ADYAZwfOAFMLfjfQSt#/?folderId=i.****************.1688856698597697_d.723012717gO1n", "category": "Tiktok", "tags": []}, {"title": "⁣‬​‌‌⁤⁣⁤‍⁡​​‍​‍﻿﻿⁢​⁣⁢‍⁤‍⁣​​‍‍​‬‬‍‌​​​‬⁢⁣​﻿⁢⁣‬‍⁡‬﻿Engu- BC注册新 - 飞书云文档", "url": "https://eg88888888.feishu.cn/slides/EKfysBJselFLKNdYu7Zc5z3Qnob?chunked=false", "category": "Tiktok", "tags": []}, {"title": "TikTok专业服务平台", "url": "https://vm.ttsop.net/dashboard/tiktok-qf-account/purchase", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "如何一键使用 ComfyUI 进行本地 FLUX.1 LoRA 的训练_comfyui-fluxtrainer-CSDN博客", "url": "https://blog.csdn.net/m0_71746299/article/details/*********", "category": "Tiktok", "tags": ["Ai"]}, {"title": "⁣‍​‍﻿‍﻿​⁡‍⁢​⁣⁢⁤​‌​⁢﻿⁤‌​⁡⁤‌​⁤⁣⁢‌﻿‍​﻿⁢​​‬﻿⁡﻿‍​﻿​﻿‌⁢跨境密码-TikTok行业资源汇总 - 飞书云文档", "url": "https://b5dqpyykxn.feishu.cn/docx/SdTDd41U8oB2CZxGMtgcxUflnDc", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "新卖家推荐费促销 - 2025 --- New Seller Referral Fee Promotion - 2025", "url": "https://seller-us.tiktok.com/university/essay?knowledge_id=****************&role=1&course_type=1&from=search&identity=1", "category": "Tiktok", "tags": ["TikTok"]}, {"title": "TikTok干货资料_TikTok资料专区_运营学习手册免费领取-TKTOC运营导航", "url": "https://www.tktoc.com/docs/document", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "Give Me Star·东南亚测评专家", "url": "https://po.givemestar.com/task/dashboard", "category": "Tiktok", "tags": []}, {"title": "出海匠 - TikTok达人搜索", "url": "https://www.chuhaijiang.com/tiktok/entity/tiktok_creator/search?is_has_product=488a6ae15b2b707bf240132069174fce", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "TikTok 带货达人广场丨嘀嗒狗", "url": "https://didadog.com/talent/square", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "Loukious/StreamLabsTikTokStreamKeyGenerator: TikTok Live Stream Key Generator for OBS Studio using Streamlabs API", "url": "https://github.com/Loukious/StreamLabsTikTokStreamKeyGenerator?tab=readme-ov-file", "category": "Tiktok", "tags": ["Tiktok", "Api", "GitHub"]}, {"title": "TikTok专业服务平台", "url": "https://vm.ttsop.cn/dashboard", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "参与电子收据易 2.0 的确认表格 --- แบบฟอร์มการยืนยันในการเข้าร่วม Easy E-Receipt 2.0", "url": "https://bytedance.sg.larkoffice.com/share/base/form/shrlg1B5cSwJ008wewFYON5EnMf", "category": "Tiktok", "tags": []}, {"title": "一、在TikTok如何建立自己的达人分销私域 - 飞书云文档", "url": "https://ia8xe4wnh3u.feishu.cn/docx/T7kAdZxCKo066WxgU8Hc3nt1nif", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "Docs", "url": "https://b5dqpyykxn.feishu.cn/drive/folder/MHYdfCj71lhFV2dGq2zckc66nAb", "category": "Tiktok", "tags": []}, {"title": "Trademark", "url": "https://www.ipthailand.go.th/en/trademark-001.html", "category": "Tiktok", "tags": []}, {"title": "关于 TikTok 上的全漏斗营销 | TikTok 广告管理平台", "url": "https://ads.tiktok.com/help/article/full-funnel-marketing-tiktok?lang=zh", "category": "Tiktok", "tags": ["Tiktok", "TikTok"]}, {"title": "TikTok Shop - Marketplace Sellers Commission Fee Rate, from February 1st 2024.pdf", "url": "file:///F:/Windows_data/Downloads/TikTok%20Shop%20-%20Marketplace%20Sellers%20Commission%20Fee%20Rate,%20from%20February%201st%202024.pdf", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "东南亚跨境佣金及交易手续费规则总览", "url": "https://seller.tiktokglobalshop.com/university/essay?knowledge_id=6173886299686658&role=1&course_type=1&from=search&identity=1", "category": "Tiktok", "tags": ["TikTok"]}, {"title": "在线下载高清质量的没有徽标、水印、watermark 的 TikTok 视频 | TikTok 视频下载器 - TikVid.io", "url": "https://tikvid.io/zh-cn", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "淘数据", "url": "https://www.taosj.com/", "category": "Tiktok", "tags": []}, {"title": "国内国外邮编查询", "url": "https://www.nowmsg.com/", "category": "Tiktok", "tags": []}, {"title": "Pigcha加速器官网", "url": "http://pigcha.com/", "category": "Tiktok", "tags": []}, {"title": "公章专家_公司电子印章图片生成器_印章在线制作大师", "url": "http://seal.ssjjss.com/", "category": "Tiktok", "tags": []}, {"title": "全球付-国际购物-在线消费新体验", "url": "https://www.globalcash.hk/v4/dashboard", "category": "Tiktok", "tags": []}, {"title": "TREND HUNTER - #1 in Trends, Trend Reports, Fashion Trends, Tech, Design", "url": "https://www.trendhunter.com/", "category": "Tiktok", "tags": []}, {"title": "马帮ERP", "url": "https://900541.private.mabangerp.com/", "category": "Tiktok", "tags": []}, {"title": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "url": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "category": "Tiktok", "tags": []}, {"title": "燕文物流", "url": "https://portal.yw56.com.cn/systemManagement/userMaintenance", "category": "Tiktok", "tags": []}, {"title": "AMZ123亚马逊导航-跨境电商出海门户", "url": "https://www.amz123.com/", "category": "Tiktok", "tags": ["电商"]}, {"title": "跨境电商语言解决方案", "url": "https://www.alifanyi.com/index.html?spm=a271w.8016606.0.0.12525e88rilcIf", "category": "Tiktok", "tags": ["电商"]}, {"title": "在线制作电子公章(专业的免费公章在线生成工具)—制图网", "url": "http://www.makepic.net/tool/signet.html", "category": "Tiktok", "tags": ["工具"]}, {"title": "Fiverr - Freelance Services Marketplace", "url": "https://www.fiverr.com/?source=top_nav", "category": "Tiktok", "tags": []}, {"title": "VQCM（微企传媒）推广资源报价表", "url": "https://ud8dvlo0p7.jiandaoyun.com/dash/64ae640e95c6cf0009ff617d", "category": "Tiktok", "tags": []}, {"title": "微企通-站外推广报价", "url": "https://shimo.im/sheets/vJVRCvCyPkxHdg3q", "category": "Tiktok", "tags": []}, {"title": "逮虾录 - 资源列表", "url": "https://daixialu.com/resource", "category": "Tiktok", "tags": []}, {"title": "PNG素材网-免费高清透明PNG素材资源分享网站_PNG图片素材下载 PngSucai.Com", "url": "https://www.pngsucai.com/", "category": "Tiktok", "tags": ["Ai"]}, {"title": "各国插座规格简介 - 创客出手", "url": "https://makeronsite.com/plug_types.html", "category": "Tiktok", "tags": []}, {"title": "站外查询 - 站外智汇", "url": "https://www.trafficwiser.com/dealsite", "category": "Tiktok", "tags": []}, {"title": "Fake Name Generator", "url": "https://namefake.com/", "category": "Tiktok", "tags": []}, {"title": "发起的举报", "url": "https://csp.aliexpress.com/m_apps/violation/pop-rptinitiated?channelId=581345", "category": "Tiktok", "tags": []}, {"title": "跨境小白 - 跨境电商公司评价网", "url": "https://kjrate.com/", "category": "Tiktok", "tags": ["电商"]}, {"title": "法国Cdiscount平台导表批量上传产品 · 跨境erp3.0使用帮助手册 · 看云", "url": "https://www.kancloud.cn/hellmen/erp3/2375856", "category": "Tiktok", "tags": []}, {"title": "福步外贸论坛(FOB Business Forum) |中国第一外贸论坛", "url": "https://bbs.fobshanghai.com/index.php", "category": "Tiktok", "tags": []}, {"title": "跨境知道卖家导航-集合跨境卖家出海所需的一切资源门户网站_跨境知道", "url": "https://tools.ikjzd.com/", "category": "Tiktok", "tags": []}, {"title": "UPC在线生成|海外仓,海外仓美国,一件代发,退货换标", "url": "https://www.haiwaicang.com/upc.html", "category": "Tiktok", "tags": []}, {"title": "专利查询", "url": "https://lin.wxjyxwt.cn/gj/?unit=guojia&keyword=gjzlgbggcx&e_creative=***********&e_adposition=cl1&e_keywordid=665384644777&e_keywordid2=606130198995&bd_vid=11217509308245480358", "category": "Tiktok", "tags": []}, {"title": "Best Product Research Tool", "url": "https://ixspy.com/data#/dashboard", "category": "Tiktok", "tags": []}, {"title": "世界各地时间换算-时间换算工具-AMZ123跨境导航", "url": "https://www.amz123.com/tools-timeconversion", "category": "Tiktok", "tags": ["工具"]}, {"title": "Seller Center", "url": "https://gsp.aliexpress.com/", "category": "Tiktok", "tags": []}, {"title": "免费UPC在线生成工具-AMZ123跨境导航", "url": "https://www.amz123.com/tools-upc", "category": "Tiktok", "tags": ["工具"]}, {"title": "Generador de DNI (NIF, NIE, CIF) - generador-de-dni.com", "url": "https://www.generador-de-dni.com/validador-de-dni", "category": "Tiktok", "tags": []}, {"title": "您的广告 ads - Cdiscount", "url": "https://marketplace.cdiscount.com/zh/service/premium-ads/", "category": "Tiktok", "tags": []}, {"title": "跨境资料库-跨境电商资料大全-雨果网", "url": "https://www.cifnews.com/links/data", "category": "Tiktok", "tags": ["电商"]}, {"title": "邮箱穷举猜测工具-福步外贸论坛", "url": "https://bbs.fobshanghai.com/emailformat/fuju.php", "category": "Tiktok", "tags": ["工具"]}, {"title": "KC_202403221504357350.pdf - 常规产品认证/KC, KTC - ID: 109955603 - Industry Support Siemens", "url": "https://support.industry.siemens.com/cs/document/109955603/%E5%B8%B8%E8%A7%84%E4%BA%A7%E5%93%81%E8%AE%A4%E8%AF%81-kc-ktc?dti=0&lc=zh-CN", "category": "Tiktok", "tags": []}, {"title": "‌‬​‌‬‌​⁣‍⁣​​‬﻿⁣​​‬⁢⁣﻿‌⁣﻿⁡‌​‬⁢﻿⁡‬⁡﻿⁤​‬‍‍⁢⁢﻿‬​‍﻿⁢‌‬‍【非服全托管模式】供应商上架推款培训 - Feishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/G1SodEkUpo7E16xZmFHcCB6jnbe", "category": "Tiktok", "tags": []}, {"title": "Certification (FCC, CE, RoHS, etc): | Holybro Docs", "url": "https://docs.holybro.com/company/certification-fcc-ce-rohs-etc", "category": "Tiktok", "tags": []}, {"title": "Dragino Download Server ./downloads/HE/certificate/CE NB Certificate Report/", "url": "https://www.dragino.com/downloads/index.php?dir=HE/certificate/CE%20NB%20Certificate%20Report/", "category": "Tiktok", "tags": []}, {"title": "全部商品页", "url": "https://shop534c776216a47.1688.com/page/offerlist.htm?spm=0.0.wp_pc_common_topnav_38229151.0", "category": "Tiktok", "tags": []}, {"title": "德国EPR", "url": "https://oeffentliche-register.verpackungsregister.org/DeclarationOfCompleteness", "category": "Tiktok", "tags": []}, {"title": "紧要通知！卖家怎么查询德国EPR号是否注册成功？-雨果网", "url": "https://www.cifnews.com/article/114800", "category": "Tiktok", "tags": []}, {"title": "EPR废弃电气设备登记基金会", "url": "https://www.ear-system.de/ear-verzeichnis/hersteller.jsf#no-back", "category": "Tiktok", "tags": []}, {"title": "Latest FCC ID - Google 表格", "url": "https://docs.google.com/spreadsheets/d/1hGKSUkayxOMrGbHA4JJTbVXBbA8e4fmhWJ_4nzEZ81c/edit?pli=1&gid=0#gid=0", "category": "Tiktok", "tags": []}, {"title": "Shenzhen Baseus Technology Baseus Security P1 Indoor Camera 3K S0TV01 FCC ID 2A482-S0TV01", "url": "https://fccid.io/2A482-S0TV01", "category": "Tiktok", "tags": []}, {"title": "Docs", "url": "https://bytedance.sg.larkoffice.com/docx/ICuFd7pcooNEb7xHpeYl5unXgze", "category": "Tiktok", "tags": []}, {"title": "‍‬​﻿⁣⁤⁡⁡⁡﻿​​⁡⁡​⁤﻿⁢​⁣⁣‌‌⁤​⁡​​​⁣⁤​⁤‌﻿‍⁢﻿​⁢​⁡​​​﻿​‍﻿手机数码【分流指引】-各类目买手对接群/订单运营群 - Feishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/RrR2dyc4loeUsFxfGqLcJl5Hnwb", "category": "Tiktok", "tags": []}, {"title": "关于加强商品资质管控的通告 - TikTok Shop全托管卖家中心学习平台", "url": "https://learning-center.fanczs.com/support/content/138952?graphId=654&hideFooter=1&hideUnify=1&mappingType=2&pageId=441&spaceId=235×tamp=1702292818830", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "pss-system.cponline.cnipa.gov.cn/conventionalSearch", "url": "https://pss-system.cponline.cnipa.gov.cn/conventionalSearch", "category": "Tiktok", "tags": []}, {"title": "电子电器类商品合规规范 - TikTok Shop全托管卖家中心学习平台", "url": "https://learning-center.fanczs.com/support/content/140923?graphId=654&hideFooter=1&hideUnify=1&mappingType=2&pageId=441&spaceId=235×tamp=1719559396800", "category": "Tiktok", "tags": ["Tiktok"]}, {"title": "免费在线下载 YouTube 视频 - Y2Down.app", "url": "https://y2down.app/zh/youtube-video-downloader-zh/#url=https://www.youtube.com/watch?v=9zZwM9z0934", "category": "Tiktok", "tags": []}, {"title": "CE certificates for Electronic Control Unit (KFG)", "url": "https://flex-homologation.com/BMW", "category": "Tiktok", "tags": []}, {"title": "FCC ID Search", "url": "https://fccid.io/", "category": "Tiktok", "tags": []}, {"title": "‍​⁣​‍⁡​‍⁣⁡⁤⁤⁣​​⁡⁢‌⁡⁣⁢⁡​﻿⁣​⁣⁢⁤​​⁢​⁢​‍​⁣‍﻿​⁢⁡⁣‬‬⁡⁡﻿​第6期 商品资质提交常见问题 - Feishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/BHP7dDveVocWV6xNfysctiuFnah", "category": "Tiktok", "tags": []}, {"title": "‌‍⁤⁢‬‬⁢⁤⁢‬⁡⁢⁡⁣⁤‌⁡​​⁣⁣​​⁣⁣⁣​⁢‍⁢​​⁢⁣​‌‍⁡⁡‌﻿﻿​⁣‌‬‌‬​﻿如何使用嘀嗒狗数据判断某个品能不能做 - Feishu Docs", "url": "https://didagogo.feishu.cn/docx/JyGndNRVioAQ4UxGb7pcF9PUnQb", "category": "Tiktok", "tags": []}, {"title": "‍⁣​​‬﻿⁢⁡⁡‍⁤​‬‍​⁤﻿⁡⁢⁡⁡⁣﻿​⁤​​⁡⁡﻿⁢﻿⁣​​⁡‌​‌⁤⁣‌⁢⁤‍﻿⁢‍​‍全托管商家-化妆品资质标签要求 - <PERSON>ishu Docs", "url": "https://bytedance.sg.larkoffice.com/docx/Usl9dSPlyom5OpxanzuchjMBnlf", "category": "Tiktok", "tags": []}, {"title": "发现 - 雨果问答-跨境电商权威知识问答平台", "url": "https://www.cifnews.com/ask", "category": "Tiktok", "tags": ["电商"]}, {"title": "美国地址生成，美国人虚构信息生成-世界各国虚拟身份信息、地址、信用卡生成", "url": "https://www.haoweichi.com/", "category": "Tiktok", "tags": []}, {"title": "泰国海外产品库", "url": "https://www.kdocs.cn/l/cdfYOfJSg6UI", "category": "Tiktok", "tags": []}, {"title": "售后联系", "url": "https://www.kdocs.cn/l/cg1W82fld68a", "category": "Tiktok", "tags": []}, {"title": "金山文档 | WPS云文档", "url": "https://www.kdocs.cn/team/2344622591?guideShowTag=true", "category": "Tiktok", "tags": []}, {"title": "美国地址生成器 - 随机生成美国地址和个人身份信息", "url": "https://www.usaddrgen.com/", "category": "Tiktok", "tags": []}, {"title": "全国企业信息查询系统", "url": "https://test1.javaw.icu/", "category": "Tiktok", "tags": []}, {"title": "JDL国内库存表格", "url": "https://www.kdocs.cn/l/caSWWxarIWrZ", "category": "Tiktok", "tags": []}, {"title": "DIP", "url": "https://search.ipthailand.go.th/index2?q=JTdCJTIycSUyMiUzQSUyMmx1bmElMjIlMkMlMjJpbmRleCUyMiUzQSUyMmRpcF9zZWFyY2hfM190bSUyMiUyQyUyMmRpc3BsYXklMjIlM0ElMjJkaXBfc2VhcmNoXyolMjIlMkMlMjJpbmRleF9jcmVhdGUlMjIlM0ElMjJkaXBfc2VhcmNoXzNfdG0lMjIlMkMlMjJpbiUyMiUzQTMlMkMlMjJvcmRlciUyMiUzQSUyMl9zY29yZSUyQ2Rlc2MlMjIlMkMlMjJ0eXBlJTIyJTNBJTIyc2VhcmNoX2FsbF9zY3MlMjIlMkMlMjJ0eXBlX25hbWUlMjIlM0ElMjIlRTAlQjklODAlRTAlQjglODQlRTAlQjglQTMlRTAlQjglQjclRTAlQjklODglRTAlQjglQUQlRTAlQjglODclRTAlQjglQUIlRTAlQjglQTElRTAlQjglQjIlRTAlQjglQTIlRTAlQjglODElRTAlQjglQjIlRTAlQjglQTMlRTAlQjglODQlRTAlQjklODklRTAlQjglQjIlMjIlMkMlMjJ0YWJfaW5kZXglMjIlM0ElMjJkaXBfc2VhcmNoXzNfdG0lMjIlMkMlMjJkZl9pbmRleCUyMiUzQSUyMmRpcF9zZWFyY2hfM190bSUyMiUyQyUyMmJ1Y2tldHMlMjIlM0ElNUIlN0IlMjJkb2NfY291bnQlMjIlM0E0NTMlMkMlMjJrZXklMjIlM0ElMjJkaXBfc2VhcmNoXzNfdG0lMjIlN0QlNUQlN0Q%3D", "category": "Tiktok", "tags": []}, {"title": "ALL-IN-ONE PACKAGE TRACKING | 17TRACK", "url": "https://www.17track.net/en", "category": "物流", "tags": []}, {"title": "4PX", "url": "http://track.4px.com/query/?locale=en_US", "category": "物流", "tags": []}, {"title": "顺友物流查询平台 | Sunyou Package Tracking", "url": "https://www.sypost.net/index.html", "category": "物流", "tags": []}, {"title": "全球物流跟踪", "url": "https://global.cainiao.com/detail.htm?mailNoList", "category": "物流", "tags": []}, {"title": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "url": "https://oms.yunexpress.cn/?token=8b7d5393d62d466086bfbba9fa057224", "category": "物流", "tags": []}, {"title": "燕文物流", "url": "https://portal.yw56.com.cn/systemManagement/userMaintenance", "category": "物流", "tags": []}, {"title": "Tracking Results | Yuntrack - YunExpress", "url": "http://new.yuntrack.com/list", "category": "物流", "tags": []}, {"title": "船讯网--船舶动态、船舶档案、AIS船位、货物跟踪、租船、OP、航运大数据", "url": "https://www.shipxy.com/", "category": "物流", "tags": ["Ai"]}, {"title": "海外刷，外贸电商互动平台的领跑者,提升流量,提升信誉,让电商钱途无限!", "url": "http://**************:8080/admin/index", "category": "物流", "tags": ["电商"]}, {"title": "注册帐号 | Ozon Help", "url": "https://docs.ozon.ru/global/zh/launch/steps/", "category": "物流", "tags": []}, {"title": "仓储管理系统", "url": "http://cititrans.yunwms.com/", "category": "物流", "tags": []}, {"title": "Fake Name Generator | FauxID.com", "url": "https://fauxid.com/fake-name-generator/south-korea?gender=male", "category": "物流", "tags": []}, {"title": "Busca CEP --- 邮政编码搜索", "url": "https://buscacepinter.correios.com.br/app/endereco/index.php?spm=a2d0d.11623551.0.0.5043253b5Uh9G0", "category": "物流", "tags": []}, {"title": "艾姆勒海外仓-订单管理系统", "url": "http://oms.imlb2c.com/", "category": "物流", "tags": []}, {"title": "首页 - 日升辉", "url": "http://rsh.itdida.com/itdida-flash/desktop/client-portal", "category": "物流", "tags": []}, {"title": "全球物流查询平台 | 17TRACK", "url": "https://www.17track.net/zh-cn", "category": "物流", "tags": []}, {"title": "首页 - OMS", "url": "https://oms-cntodd.xlwms.com/", "category": "物流", "tags": []}, {"title": "鹏城物流系统", "url": "http://pc.kingtrans.net/nclient/Logon?action=initMenu", "category": "物流", "tags": []}, {"title": "仓储管理系统", "url": "https://cititrans.yunwms.com/", "category": "物流", "tags": []}, {"title": "FreeBuf.COM | 关注黑客与极客", "url": "http://www.freebuf.com/", "category": "android", "tags": []}, {"title": "Java 教程 | 菜鸟教程", "url": "http://www.runoob.com/java/java-tutorial.html", "category": "android", "tags": ["教程"]}, {"title": "【动态ab分区】动态分区解锁system而能与lsp或edxp框架共存（小白保姆级别教程） 来自 Kpfc白中白 - 酷安", "url": "https://www.coolapk.com/feed/27017797?shareKey=MzUzYzI1NDkwYTMzNjBmMDVhZTg~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "category": "android", "tags": ["教程"]}, {"title": "termux配置DNA小白保姆级别安装教程（内含出错情况的解决方案） 来自 Kpfc白中白 - 酷安", "url": "https://www.coolapk.com/feed/26779553?shareKey=MWRiNzY4MmJiN2QzNjBmMDYyZDI~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "category": "android", "tags": ["教程"]}, {"title": "Android verified boot 2.0 vbmeta 数据结构解析 - 简书", "url": "https://www.jianshu.com/p/a2542426bdde", "category": "android", "tags": []}, {"title": "出厂安卓11的机型之VAB架构的详细分析 来自 Rannki - 酷安", "url": "https://www.coolapk.com/feed/27525474?shareKey=ODE0ODY2MGNmY2NkNjBmNjI3M2Y~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "category": "android", "tags": []}, {"title": "出厂安卓11机型解锁system分区教程 来自 Rannki - 酷安", "url": "https://www.coolapk.com/feed/26090868?shareKey=MDM1NzZjZjFkMWVlNjEwNmNkOGQ~&shareUid=431416&shareFrom=com.coolapk.market_11.2.8", "category": "android", "tags": ["教程"]}, {"title": "来自 响當當 - 酷安", "url": "https://www.coolapk.com/feed/27072044?shareKey=YWM1OTM5MDUwYjg0NjEwN2IxZDc~&shareUid=431416&shareFrom=com.coolapk.market_11.2.7-beta2", "category": "android", "tags": []}, {"title": "菜鸡互啄--D.N.A解密官方包图文经验参考 来自 かかか - 酷安", "url": "https://www.coolapk.com/feed/28976999?shareKey=ZmVkZTA2YTNmNGEzNjExODA1MDI~&shareUid=431416&shareFrom=com.coolapk.market_11.3", "category": "android", "tags": []}, {"title": "SuperR Kitchen | XDA Forums", "url": "https://forum.xda-developers.com/f/superr-kitchen.6337/", "category": "android", "tags": []}, {"title": "V-AB机型无视版本和系统更新限制，手动命令刷任意版本的官方ROM 来自 残芯此生不换_TWRP - 酷安", "url": "https://www.coolapk.com/feed/29368917?shareKey=ZWFmZWEyN2ZmYTIxNjEyMGE0MmE~&shareUid=431416&shareFrom=com.coolapk.market_11.3.1-beta1", "category": "android", "tags": []}, {"title": "系统更新偷渡器修改教程 来自 柚稚的孩纸 - 酷安", "url": "https://www.coolapk.com/feed/28372396?shareKey=Y2FhNTZlMmVkNDBlNjEyMGFmYmY~&shareUid=431416&shareFrom=com.coolapk.market_11.3.1-beta1", "category": "android", "tags": ["教程"]}, {"title": "Fastboot Enhance —— 适合人类使用的Fastboot + Payload.bin 解包工具箱 - AKR社区", "url": "https://www.akr-developers.com/d/506", "category": "android", "tags": ["工具"]}, {"title": "开发 Android 设备  |  Android 开源项目  |  Android Open Source Project", "url": "https://source.android.google.cn/devices", "category": "android", "tags": ["开源"]}, {"title": "一起来编译！面对小白的ROM编译教程（完全体）（可能是唯一有用的ROM编译教程（？）） 来自 SakuraiKR - 酷安", "url": "https://www.coolapk.com/feed/24966438?shareKey=MWM3NzNhMjUyNDcyNjE2NzA5ZDA~", "category": "android", "tags": ["Ai", "教程"]}, {"title": "给红米Note 4X编译LineageOS 14.1刷机包过程【详细】_skyshell的专栏-CSDN博客", "url": "https://blog.csdn.net/fftt516/article/details/78160488/", "category": "android", "tags": []}, {"title": "自己动手编译Android(LineageOS)源码 - 刷机党资源网", "url": "https://www.irom.net/post/42.html", "category": "android", "tags": []}, {"title": "cm13编译中的local manifest写法_高飞的专栏-CSDN博客", "url": "https://blog.csdn.net/feiniao8651/article/details/70162840", "category": "android", "tags": []}, {"title": "如何从Android固件文件中提取设备树文件 - 简书", "url": "https://www.jianshu.com/p/ceb407ffe9e5", "category": "android", "tags": []}, {"title": "[TOOL] Android Image Kitchen - Unpack/Repack Kernel Ramdisk [Win/Android/Linux/Mac] | XDA Forums", "url": "https://forum.xda-developers.com/t/tool-android-image-kitchen-unpack-repack-kernel-ramdisk-win-android-linux-mac.2073775/", "category": "android", "tags": []}, {"title": "XiaomiROM.com - 小米 ROM 线刷包, 卡刷包的最新及历史版本下载", "url": "https://xiaomirom.com/", "category": "android", "tags": []}, {"title": "Treble-Enabled Device Development A/AB ROMS | XDA Forums", "url": "https://forum.xda-developers.com/f/treble-enabled-device-development-a-ab-roms.7260/", "category": "android", "tags": []}, {"title": "UnlockTool 2022.06.24.1 Update Link Setup Free Download", "url": "https://gsmxt.com/unlocktool-update-link-setup-free-download/", "category": "android", "tags": []}, {"title": "Mediatek Metamode Native protocol Source Code - GSM Alphabet | Buy Source", "url": "https://alephgsm.com/2021/12/19/mediatek-metamode-native-protocol-source-code/", "category": "android", "tags": []}, {"title": "Win11安卓子系统 WSA 安装教程，Win11安装安卓应用 亲测有效！ – 优质盒子", "url": "https://uzbox.com/tech/wsa-pacman.html", "category": "android", "tags": ["教程"]}, {"title": "在非gki内核中添加KernelSU支持_如何为非 gki 内核集成 kernelsu-CSDN博客", "url": "https://blog.csdn.net/qq_43283565/article/details/137374337", "category": "android", "tags": []}, {"title": "🏹 在小米平板 5 上安装 Arch Linux - 風雪城", "url": "https://blog.chyk.ink/2024/06/22/mipad5-archlinux/", "category": "android", "tags": []}, {"title": "timoxa0/Switch2Linux-Nabu: Moved to https://g.tx0.su/timoxa0/Switch2Linux-Nabu", "url": "https://github.com/timoxa0/Switch2Linux-Nabu?tab=readme-ov-file", "category": "android", "tags": ["GitHub"]}, {"title": "构建用于 Waydroid 的 LineageOS20 系统 - InSnh-Gd", "url": "https://xlog.insnhgd.com/2?locale=zh", "category": "android", "tags": []}, {"title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/Simplified Chinese/selection-cn.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-<PERSON>d-5/blob/main/guide/Simplified%20Chinese/selection-cn.md", "category": "android", "tags": ["Ai", "GitHub"]}, {"title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/Simplified Chinese/won-deployer-install-cn.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5/blob/main/guide/Simplified%20Chinese/won-deployer-install-cn.md", "category": "android", "tags": ["Ai", "GitHub"]}, {"title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/English/installation-selection-en.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-<PERSON>-5/blob/main/guide/English/installation-selection-en.md", "category": "android", "tags": ["Ai", "GitHub"]}, {"title": "Port-Windows-11-<PERSON><PERSON>-Pad-5/guide/English/won-deployer-install-en.md at main · erdilS/Port-Windows-11-<PERSON><PERSON>-Pad-5", "url": "https://github.com/erdilS/Port-Windows-11-<PERSON><PERSON>-<PERSON>d-5/blob/main/guide/English/won-deployer-install-en.md", "category": "android", "tags": ["Ai", "GitHub"]}, {"title": "timoxa0/Switch2Linux-Nabu: App for linux dualboot on Nabu (Xiaomi Pad 5) - timoxa0's Forgejo instance", "url": "https://g.tx0.su/timoxa0/Switch2Linux-Nabu", "category": "android", "tags": []}, {"title": "Download TWRP for cheeseburger_dumpling", "url": "https://dl.twrp.me/cheeseburger_dumpling/", "category": "android", "tags": []}, {"title": "ChimeProjects - Browse /Pixel Projects at SourceForge.net", "url": "https://sourceforge.net/projects/chimeprojects/files/Pixel%20Projects/", "category": "android", "tags": []}, {"title": "Triple-Boot-on-Xiaomi-Pad-5/guide/Install-LINUX.md at main · Xyy155/Triple-Boot-on-Xiaomi-Pad-5", "url": "https://github.com/Xyy155/<PERSON>-Boot-on-Xiaomi-Pad-5/blob/main/guide/Install-LINUX.md", "category": "android", "tags": ["Ai", "GitHub"]}, {"title": "Featured Wallpapers - Wallpaper Abyss - Page 28", "url": "http://wall.alphacoders.com/featured.php?page=28", "category": "设计", "tags": []}, {"title": "Banner设计 - 优优教程网", "url": "https://uiiiuiii.com/inspirations/banner#inspiration-menu?tdsourcetag=s_pctim_aiomsg", "category": "设计", "tags": ["教程"]}, {"title": "网页设计常用色彩搭配表 - 配色表 | 小影的工具箱", "url": "http://tool.c7sky.com/webcolor/#character_6", "category": "设计", "tags": ["工具"]}, {"title": "Photoshop-LookAE.com", "url": "https://www.lookae.com/qitarjcj/pszy/", "category": "设计", "tags": []}, {"title": "An Ultimate list of 500 AI tools", "url": "https://spectacular-party-fc2.notion.site/An-Ultimate-list-of-500-AI-tools-8f737bef33af49fc97336dc9c819c695", "category": "设计", "tags": ["Ai"]}, {"title": "免费正版高清图片素材库 超过2.7百万张优质图片和视频素材可供免费使用和下载 - Pixabay - Pixabay", "url": "https://pixabay.com/zh/", "category": "设计", "tags": []}, {"title": "Mega Creator – Easy & Free Online Graphic Design Software", "url": "https://icons8.com/mega-creator/dashboard", "category": "设计", "tags": []}, {"title": "100,000+张最精彩的“Sky”图片 · 100%免费下载 · Pexels素材图片", "url": "https://www.pexels.com/zh-cn/search/sky/", "category": "设计", "tags": []}, {"title": "Mp4电影_最新电影下载_最新高清MP4电影资源下载", "url": "https://www.domp4.cc/", "category": "media", "tags": []}, {"title": "MyFreeMP3", "url": "https://tool.liumingye.cn/music/#/explore/artist", "category": "media", "tags": []}, {"title": "Xslist.org - 健康的宅男偶像专题网站", "url": "https://xslist.org/zh", "category": "media", "tags": []}, {"title": "ASMR Online", "url": "https://www.asmr.one/works", "category": "media", "tags": []}, {"title": "Torrent Kitty - Free Torrent To Magnet Link Conversion Service", "url": "https://www.torkitty.com/search/", "category": "media", "tags": []}, {"title": "死神 千年血战篇-诀别谭 第04集 - 在线播放 - AGE动漫", "url": "https://www.agedm.org/play/20230135/1/4", "category": "media", "tags": []}, {"title": "MissAV | Watch HD JAV Online | Free & High Quality AV", "url": "https://missav123.com/dm22/en", "category": "media", "tags": []}, {"title": "domain-list-community/data/category-porn at master · v2ray/domain-list-community", "url": "https://github.com/v2ray/domain-list-community/blob/master/data/category-porn", "category": "media", "tags": ["Ai", "GitHub"]}, {"title": "影视站合集 短剧在线集合 在线播放 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/181385", "category": "media", "tags": ["Linux"]}, {"title": "原版软件", "url": "https://next.itellyou.cn/Original/Index", "category": "tool", "tags": []}, {"title": "登录 - 中国电信网上营业厅·广东", "url": "https://gd.189.cn/common/login.htm?loginOldUri=/service/pay/", "category": "tool", "tags": []}, {"title": "广东电信宽带测速平台", "url": "https://10000.gd.cn/#/speed", "category": "tool", "tags": []}, {"title": "Upload Image – remove.bg", "url": "https://www.remove.bg/upload?full_image_id=e0d038ce-15c2-4882-afab-f293f636db2b", "category": "tool", "tags": []}, {"title": "WPS - 搜索结果 - 果核剥壳", "url": "https://www.ghxi.com/?s=WPS", "category": "tool", "tags": []}, {"title": "XIU2/TrackersListCollection", "url": "https://trackerslist.com/#/zh", "category": "tool", "tags": []}, {"title": "(2023.9.5)SHELL脚本：一键给PVE增加温度,cpu功耗频率,硬盘等信息-软路由,x86系统,openwrt(x86),Router OS 等-恩山无线论坛", "url": "https://www.right.com.cn/forum/thread-6754687-1-1.html", "category": "tool", "tags": []}, {"title": "Windows10专业版免费永久激活（亲测可用） - One heart - 博客园", "url": "https://www.cnblogs.com/oneheart/p/17231732.html", "category": "tool", "tags": []}, {"title": "totoroterror/warp-cloner: Simple Python script that can clone Warp Plus (*******) keys and generate 12PB (or 24PB) keys.", "url": "https://github.com/totoroterror/warp-cloner/tree/main", "category": "tool", "tags": ["GitHub"]}, {"title": "Anaconda创建环境、删除环境、激活环境、退出环境_conda 删除环境-CSDN博客", "url": "https://blog.csdn.net/H_O_W_E/article/details/77370456", "category": "tool", "tags": []}, {"title": "lmc999/auto-add-routes: China Route for VPN", "url": "https://github.com/lmc999/auto-add-routes", "category": "tool", "tags": ["GitHub"]}, {"title": "WARPconfig-youtube不一样的强哥 - Replit", "url": "https://replit.com/@304070820/WARPconfig-youtubeB<PERSON>-<PERSON>-<PERSON>-<PERSON>-<PERSON>e", "category": "tool", "tags": []}, {"title": "【攻破了】WARP免费VPN一键实现网络分流，消除网友痛点！超详细教学网络代理分流！Warp+帐号生成、配置。安卓手机和window warp网络代理分流（WARP第四期） - YouTube", "url": "https://www.youtube.com/watch?v=Ll3T0AWtPWE", "category": "tool", "tags": []}, {"title": "3款来自于 github.com 的 Windows 激活工具_技术攀登者的技术博客_51CTO博客", "url": "https://blog.51cto.com/u_9843231/6149019", "category": "tool", "tags": ["工具", "<PERSON><PERSON><PERSON>"]}, {"title": "massgravel/Microsoft-Activation-Scripts: A Windows and Office activator using HWID / Ohook / KMS38 / Online KMS activation methods, with a focus on open-source code and fewer antivirus detections.", "url": "https://github.com/massgravel/Microsoft-Activation-Scripts", "category": "tool", "tags": ["GitHub"]}, {"title": "Genshin Impact | GI | Mods & Resources", "url": "https://gamebanana.com/games/8552", "category": "tool", "tags": []}, {"title": "[2024.04.15更新] VCMI 1.5.0多平台中文版-英雄无敌3-WoG中文站 - Powered by <PERSON><PERSON>!", "url": "https://www.h3wog.com/thread-74994-1-1.html", "category": "tool", "tags": []}, {"title": "跨境AI亚马逊指令模板集合（一） - 知无不言跨境电商社区", "url": "https://www.wearesellers.com/article/18025", "category": "tool", "tags": ["Ai", "电商"]}, {"title": "Dragino Download Server ./downloads/LGT_92/Certificate/Anatel/", "url": "https://www.dragino.com/downloads/index.php?dir=LGT_92/Certificate/Anatel/", "category": "tool", "tags": []}, {"title": "ANATEL_8321_CERT_HOMOLOGACAO.pdf - 无线电设备型式认证证书, 无线电设备型式认证证书 - ID: 109747330 - Industry Support Siemens", "url": "https://support.industry.siemens.com/cs/document/109747330/%E6%97%A0%E7%BA%BF%E7%94%B5%E8%AE%BE%E5%A4%87%E5%9E%8B%E5%BC%8F%E8%AE%A4%E8%AF%81%E8%AF%81%E4%B9%A6-%E6%97%A0%E7%BA%BF%E7%94%B5%E8%AE%BE%E5%A4%87%E5%9E%8B%E5%BC%8F%E8%AE%A4%E8%AF%81%E8%AF%81%E4%B9%A6?dti=0&dl=zh&lc=en-BY", "category": "tool", "tags": []}, {"title": "Hikvision's FCC Supplier's Declaration of Conformity (SDOC) - Documents - Hikvision", "url": "https://www.hikvision.com/us-en/support/document-center/fcc-sdoc/", "category": "tool", "tags": []}, {"title": "HYPEROSPRO - Browse /WEEKLY at SourceForge.net", "url": "https://sourceforge.net/projects/hyperospro/files/WEEKLY/", "category": "tool", "tags": []}, {"title": "<PERSON><PERSON>", "url": "https://serverapi.cery.cloud/#/knowledge", "category": "tool", "tags": []}, {"title": "成就百科 - JX3BOX", "url": "https://www.jx3box.com/cj/view/5176", "category": "tool", "tags": []}, {"title": "使用 Ventoy 引导的 WTG 制作（试验性） | <PERSON><PERSON><PERSON><PERSON>'s Blog", "url": "https://hui-shao.com/ventoy-wtg/", "category": "tool", "tags": []}, {"title": "Tailscale 的 DERP 中继服务搭建与配置 - 白日梦观察", "url": "https://blog.angustar.com/archives/Tailscale-DERP.html", "category": "tool", "tags": ["Ai"]}, {"title": "CodeWithGPU | 能复现才是好算法", "url": "https://www.codewithgpu.com/image", "category": "tool", "tags": []}, {"title": "创建无人值守、高度自定义的纯净 Windows 11/10 系统镜像！ – 零度解说", "url": "https://www.freedidi.com/13121.html", "category": "tool", "tags": []}, {"title": "My IP Address - BrowserLeaks", "url": "https://browserleaks.com/ip", "category": "tool", "tags": []}, {"title": "GitHub - LSPosed/MagiskOnWSALocal: Integrate Magisk root and Google Apps into WSA (Windows Subsystem for Android)", "url": "https://github.com/LSPosed/MagiskOnWSALocal", "category": "tool", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "Windows安装WSL2精简版教程_wsl2安装-CSDN博客", "url": "https://blog.csdn.net/u011436427/article/details/135673366", "category": "tool", "tags": ["教程"]}, {"title": "WSL2 中访问宿主机 Windows 的代理 - ZingLix Blog", "url": "https://zinglix.xyz/2020/04/18/wsl2-proxy/", "category": "tool", "tags": []}, {"title": "Windows Android 子系统 WSA 代理设置方法教程 - 秋风于渭水", "url": "https://www.tjsky.net/tutorial/391", "category": "tool", "tags": ["教程"]}, {"title": "『WSL』如何停止或重启 WSL | NX の 博客", "url": "https://nickxu.me/2022/03/17/%E3%80%8EWSL%E3%80%8F%E5%A6%82%E4%BD%95%E5%81%9C%E6%AD%A2%E6%88%96%E9%87%8D%E5%90%AF-WSL/", "category": "tool", "tags": []}, {"title": "将 AVIF 转换为 JPG", "url": "https://converter.11zon.com/zh-cn/avif-to-jpg/", "category": "tool", "tags": []}, {"title": "<PERSON>", "url": "https://claude.ai/new", "category": "tool", "tags": []}, {"title": "My Numbers | NumberBarn", "url": "https://www.numberbarn.com/account/numbers", "category": "tool", "tags": []}, {"title": "美国电话卡Ultra Mobile Paygo套餐（3刀神卡）购买、激活及充值教程-VPS大玩家", "url": "https://www.vpsdawanjia.com/4989.html#Numberbarn", "category": "tool", "tags": ["教程"]}, {"title": "minihub/LTSC-Add-MicrosoftStore: Add Windows Store to Windows 11 24H2 LTSC", "url": "https://github.com/minihub/LTSC-Add-MicrosoftStore", "category": "tool", "tags": ["GitHub"]}, {"title": "[021]windows 11 安装 conda - 少数派", "url": "https://sspai.com/post/75430", "category": "tool", "tags": []}, {"title": "Jupyter 多环境选择以及使用方法_jupyternotebook怎么选择环境-CSDN博客", "url": "https://blog.csdn.net/zj_xd/article/details/*********", "category": "tool", "tags": []}, {"title": "丸子君的新基地尊享版教程（看完哦❤杂鱼~❤）", "url": "https://docs.qq.com/aio/DSGdQc3htbFJjSFdO?p=DXpTjzl2kZwBjN7jlRMkRJ", "category": "tool", "tags": ["教程"]}, {"title": "Sunshine 基地版串流食用指南", "url": "https://docs.qq.com/aio/DSGdQc3htbFJjSFdO?p=YTpMj5JNNdB5hEKJhhqlSB", "category": "tool", "tags": []}, {"title": "天翼一号2021官改包的简单体验 来自 负平生 - 酷安", "url": "https://www.coolapk.com/feed/58340359", "category": "tool", "tags": []}, {"title": "Windows ARM software | ‎Home", "url": "https://armrepo.ver.lt/", "category": "tool", "tags": []}, {"title": "Honkai Star Rail | HSR | Mods & Resources", "url": "https://gamebanana.com/games/18366", "category": "tool", "tags": ["Ai"]}, {"title": "成功让 nvidia tesla 工作在 WDDM（打游戏）模式的方法_服务软件_什么值得买", "url": "https://post.smzdm.com/p/a90q6055/", "category": "tool", "tags": []}, {"title": "xubiaolin/docker-zerotier-planet: 一分钟私有部署zerotier-planet服务", "url": "https://github.com/xubiaolin/docker-zerotier-planet", "category": "tool", "tags": ["GitHub"]}, {"title": "zerotier开启问题-OPENWRT专版-恩山无线论坛", "url": "https://www.right.com.cn/forum/thread-306636-1-1.html", "category": "tool", "tags": []}, {"title": "收集整理的2024最新的常用VPS脚本工具 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/165688", "category": "tool", "tags": ["工具", "Linux"]}, {"title": "Proxmox VE 7 配置 nvidia vGPU (vgpu_unlock) · woniuzfb/iptv Wiki · GitHub", "url": "https://github.com/woniuzfb/iptv/wiki/Proxmox-VE-7-%E9%85%8D%E7%BD%AE-nvidia-vGPU-(vgpu_unlock)", "category": "tool", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "PVE8 直通 debian12 | 电脑硬件学习笔记", "url": "https://skyao.io/learning-computer-hardware/graphics/p102/pve-debian12/", "category": "tool", "tags": []}, {"title": "nVidia PCI id database — envytools git documentation", "url": "https://envytools.readthedocs.io/en/latest/hw/pciid.html", "category": "tool", "tags": []}, {"title": "把WPS/Excel里的单元格为图片url链接转换为图片显示_wps链接转图片-CSDN博客", "url": "https://blog.csdn.net/hong_rui/article/details/130641869", "category": "tool", "tags": []}, {"title": "WildCard | 一分钟注册，轻松订阅海外软件服务", "url": "https://bewildcard.com/card", "category": "tool", "tags": []}, {"title": "FLUX.1 AI绘画小助手", "url": "https://share.fastgpt.in/chat/share?shareId=zlbbyirumxf8j0rpazajy96s", "category": "tool", "tags": ["Ai"]}, {"title": "宝可梦星云", "url": "https://web2.52pokemon.cc/dashboard", "category": "tool", "tags": []}, {"title": "全自动永久免费获取机场节点/订阅之最新食用方法 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/96234", "category": "tool", "tags": ["Linux"]}, {"title": "技术版块", "url": "https://www.nodeseek.com/categories/tech", "category": "tool", "tags": []}, {"title": "Win客户端剪印专业版VIP破解的小方法 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/388042", "category": "tool", "tags": ["Linux"]}, {"title": "全自动获取免费机场节点/订阅方法分享之青龙面板实现全自动订阅方式 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/303327/3", "category": "tool", "tags": ["Linux"]}, {"title": "GPTs/tutorials/【终极指南】Cursor 从安装到高效使用的完整教程.md at main · ljiao189/GPTs", "url": "https://github.com/ljiao189/GPTs/blob/main/tutorials/%E3%80%90%E7%BB%88%E6%9E%81%E6%8C%87%E5%8D%97%E3%80%91Cursor%20%E4%BB%8E%E5%AE%89%E8%A3%85%E5%88%B0%E9%AB%98%E6%95%88%E4%BD%BF%E7%94%A8%E7%9A%84%E5%AE%8C%E6%95%B4%E6%95%99%E7%A8%8B.md", "category": "tool", "tags": ["Ai", "教程", "GitHub"]}, {"title": "xubiaolin/docker-zerotier-planet: 一分钟私有部署zerotier-planet服务", "url": "https://github.com/xubiaolin/docker-zerotier-planet?tab=readme-ov-file#45-openwrt-%E5%AE%A2%E6%88%B7%E7%AB%AF%E9%85%8D%E7%BD%AE", "category": "tool", "tags": ["GitHub"]}, {"title": "Domain Name Search | Free Domain Availability Tool - Spaceship", "url": "https://www.spaceship.com/domain-search/?query=qiucat&beast=false&tab=domains", "category": "tool", "tags": ["Ai"]}, {"title": "【教程】小白也能看懂的自建Cloudflare临时邮箱教程（域名邮箱） - 文档共建 - LINUX DO", "url": "https://linux.do/t/topic/316819", "category": "tool", "tags": ["教程", "Linux"]}, {"title": "根据站里佬友的步骤注册甲骨文成功了 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/420432", "category": "tool", "tags": ["Linux"]}, {"title": "Tesla M40 24G 相关调试及应用 - 哔哩哔哩", "url": "https://www.bilibili.com/opus/800273925016649729", "category": "tool", "tags": []}, {"title": "x99主板四通道ddr3 ecc内存超频2133调时序，跑20万内存分，搭配简介使用。e5 2666v3、e5 2673v3跑分图_哔哩哔哩_bilibili", "url": "https://www.bilibili.com/video/BV1rqxqeYEff/?spm_id_from=333.337.search-card.all.click&vd_source=0730c911268ff523fed5714b68099a2d", "category": "tool", "tags": []}, {"title": "全国企业查询系统", "url": "https://javaw.icu/", "category": "tool", "tags": []}, {"title": "剪映国际版", "url": "https://editor-api-sg.capcut.com/service/settings/v3/?device_platform=windows&channel=capcutpc_0&os_version=10.0.22631&pc_gl_version=3.0&aid=359289&version_code=198656&rom_version=1431&language=en®ion=CN", "category": "tool", "tags": []}, {"title": "Download K-Lite Codec Pack", "url": "https://codecguide.com/download_kl.htm", "category": "tool", "tags": []}, {"title": "云主机-控制台", "url": "https://console.cloud.tencent.com/cvm/index", "category": "VPS", "tags": []}, {"title": "Hurricane Electric Hosted DNS", "url": "https://dns.he.net/index.cgi#", "category": "VPS", "tags": []}, {"title": "多个地点Ping服务器,网站测速 - 站长工具", "url": "http://ping.chinaz.com/", "category": "VPS", "tags": ["工具"]}, {"title": "handsome主题", "url": "https://auth.ihewro.com/support/index.html", "category": "VPS", "tags": []}, {"title": "Xray X-ui可视化管理面板+宝塔面板 共存 nginx反代实现vless+vmess+websocks+tls - It小小鸟 itxiaoniao.cn", "url": "https://zszmm.com/archives/645/", "category": "VPS", "tags": []}, {"title": "服务器详情 - LightNode", "url": "https://console.lightnode.com/product/instance/detail?ecsResourceUUID=ecs-ww00006oy6ox®ionCode=my-kualalumpur-1&zoneCode=my-kualalumpur-1-a", "category": "VPS", "tags": []}, {"title": "管理产品 - VMISS", "url": "https://app.vmiss.com/clientarea.php?action=productdetails&id=9721", "category": "VPS", "tags": []}, {"title": "客戶中心 - RackNerd LLC", "url": "https://my.racknerd.com/clientarea.php?action=productdetails&id=278835", "category": "VPS", "tags": []}, {"title": "bin456789/reinstall: 一键DD/重装脚本 (One-click reinstall OS on VPS)", "url": "https://github.com/bin456789/reinstall", "category": "VPS", "tags": ["GitHub"]}, {"title": "萤光云管理控制台", "url": "https://console.ygcloud.com/dashboard/product/ecs/example", "category": "VPS", "tags": []}, {"title": "Fail2ban安装以及配置_fail2ban参数-CSDN博客", "url": "https://blog.csdn.net/dudu1225/article/details/121242917", "category": "VPS", "tags": ["Ai"]}, {"title": "London UK - VPS Servers - Kuroit VPS & Dedicated Servers Provider", "url": "https://my.kuroit.com/clientarea.php?action=productdetails&id=25175", "category": "VPS", "tags": []}, {"title": "WebRTC Leak Test - BrowserLeaks", "url": "https://browserleaks.com/webrtc", "category": "VPS", "tags": []}, {"title": "Ubuntu 20.04.4 Server 图文安装[含磁盘分区]_ubuntu2004server安装教程-CSDN博客", "url": "https://blog.csdn.net/llm_hao/article/details/124522423", "category": "VPS", "tags": ["教程"]}, {"title": "【配置优化】我拿到VPS服务器必做的那些事 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/160305", "category": "VPS", "tags": ["Linux"]}, {"title": "【全网首发】Xray通过反向代理使用别人的家庭宽带住宅IP、手机移动网络IP，获得任意国家最纯净的网络环境，跨境电商出海必备技能，windows住宅IP推荐vps，无限家宽住宅ipv6地址 - YouTube", "url": "https://www.youtube.com/watch?v=7VVMTX8iXbg&t=588s", "category": "VPS", "tags": ["电商"]}, {"title": "云服务器购买 - AkileCloud", "url": "https://akile.io/shop/server?type=traffic&areaId=3&nodeId=2&planId=864&aff_code=e72c3dfe-6c5d-4cbf-9fe5-1ca5c76d4373", "category": "VPS", "tags": []}, {"title": "管理产品 - CLAWCLOUD", "url": "https://claw.cloud/clientarea.php?action=productdetails&id=38061", "category": "VPS", "tags": []}, {"title": "泰国云服务器-泰国云主机-泰国VPS-朝暮数据", "url": "https://www.zhaomu.com/cloud-th-country", "category": "VPS", "tags": []}, {"title": "ReadyIDC : Service 24 Hour Support", "url": "https://customer.readyidc.com/index.php?/cart/", "category": "VPS", "tags": []}, {"title": "每个国家和州（美国）的顶级网络托管公司", "url": "https://www.whtop.com/zh/companies", "category": "VPS", "tags": []}, {"title": "Debian 12 修改 SSH 登录端口和密码 - 𝐌𝐈𝐒𝐀𝐊𝐀.𝐄𝐒", "url": "https://misaka.es/archives/32.html", "category": "VPS", "tags": []}, {"title": "Facet Analysis", "url": "https://www.shodan.io/search/facet?facet=isp&query=http.html%3Aassets%2Fqs%2Fqs.min.js+country%3A", "category": "VPS", "tags": []}, {"title": "IP Address Fraud Check", "url": "https://scamalytics.com/ip", "category": "VPS", "tags": []}, {"title": "Trusted IP Data Provider, from IPv6 to IPv4 - IPinfo.io", "url": "https://ipinfo.io/", "category": "VPS", "tags": []}, {"title": "[优化]写给小白的自建2$/月的US原生家宽ip/HK节点解决方案 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/482315", "category": "VPS", "tags": ["Linux"]}, {"title": "【FastGPT】【Dify】【n8n】教程帖资源合集 附公益API - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/458195", "category": "VPS", "tags": ["教程", "Api", "Linux"]}, {"title": "NodeSeek", "url": "https://www.nodeseek.com/", "category": "VPS", "tags": []}, {"title": "3x-ui/README.zh_CN.md at main · MHSanaei/3x-ui", "url": "https://github.com/MHSanaei/3x-ui/blob/main/README.zh_CN.md", "category": "VPS", "tags": ["Ai", "GitHub"]}, {"title": "新标签页", "url": "https://console.evoxt.com/vmcontrolpanel.php?id=631168", "category": "VPS", "tags": []}, {"title": "全球主机监控 - 国内外VPS、云服务器的库存监控和优惠信息", "url": "https://stock.hostmonit.com/", "category": "VPS", "tags": []}, {"title": "免费薅AWS云服务器羊毛的完整指南（2025年更新）", "url": "https://www.nodeseek.com/post-263015-1", "category": "VPS", "tags": []}, {"title": "OpenRouter", "url": "https://openrouter.ai/", "category": "VPS", "tags": []}, {"title": "webshare.io家宽包年8美元与clash链式代理部署教程，可以一定程度解决ChatGPT降智与注册较高风控账号 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/445909/11", "category": "VPS", "tags": ["教程", "Linux"]}, {"title": "我也来分享我注册乌龟的一些经验 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/447604/11", "category": "VPS", "tags": ["Linux"]}, {"title": "IPRoyal | Premium Quality Proxies, Unbeatable Prices", "url": "https://iproyal.com/", "category": "VPS", "tags": []}, {"title": "OpenWebUI优化: models 图像批量替换成 CDN 地址脚本分享 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/554075/6", "category": "VPS", "tags": ["Linux"]}, {"title": "首页 - Canva可画", "url": "https://www.canva.com/", "category": "VPS", "tags": []}, {"title": "Support - Infomaniak", "url": "https://manager.infomaniak.com/v3/1333125/ng/support-premium/dashboard", "category": "VPS", "tags": []}, {"title": "使用耗子面板搭建全功能彩虹聚合DNS管理系统教程 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/557175", "category": "VPS", "tags": ["教程", "Linux"]}, {"title": "有人想知道google永久免费小鸡，那我来讲讲吧 - 福利羊毛 / 福利羊毛, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/367915", "category": "VPS", "tags": ["Linux"]}, {"title": "零基础学会如何创建第一个属于自己的chrome插件【定时刷新页面、防止WebRTC泄漏】 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/492063/6", "category": "VPS", "tags": ["Linux"]}, {"title": "✅Get a Free .EDU Email in 2025 | Step-by-Step Guide - YouTube", "url": "https://www.youtube.com/watch?v=Xz-qxShAb90&t=601s", "category": "VPS", "tags": ["Ai"]}, {"title": "泰国地址生成器 - 泰国身份生成器 - 泰国信用卡生成器", "url": "https://www.meiguodizhi.com/th-address", "category": "VPS", "tags": []}, {"title": "请稍候…", "url": "https://www.fakexy.com/", "category": "VPS", "tags": []}, {"title": "出生日期——美国身份查询", "url": "https://www.myheritage.com/names/john_branski", "category": "VPS", "tags": []}, {"title": "netcup 复活节彩蛋射线-自动导航彩蛋！", "url": "https://www.nodeseek.com/post-317510-1", "category": "VPS", "tags": []}, {"title": "Fake Name Generator | FauxID.com", "url": "https://fauxid.com/fake-name-generator/united-states?age=18-24&gender=female", "category": "VPS", "tags": []}, {"title": "truepeoplesearch.com/find/person/pr9n0209lu2ur0200nuu", "url": "https://www.truepeoplesearch.com/find/person/pr9n0209lu2ur0200nuu", "category": "VPS", "tags": []}, {"title": "Dashboard - ProHosting24", "url": "https://prohosting24.de/cp/", "category": "VPS", "tags": []}, {"title": "HA SSD VPS - ProHosting24", "url": "https://prohosting24.de/cp/vserver/details/8721", "category": "VPS", "tags": []}, {"title": "在debian12基础上安装proxmox8 | 求VPS", "url": "https://www.qiuvps.com/2030.html", "category": "VPS", "tags": []}, {"title": "帮你们节省85块钱 - 福利羊毛 / 福利羊毛, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/255722", "category": "VPS", "tags": ["Linux"]}, {"title": "UFW+IPSET：打造高效IP黑名单访问封锁利器 | 胡说八道", "url": "https://www.5dzone.com/posts/ufw-ipset%E6%89%93%E9%80%A0%E9%AB%98%E6%95%88ip%E9%BB%91%E5%90%8D%E5%8D%95%E8%AE%BF%E9%97%AE%E5%B0%81%E9%94%81%E5%88%A9%E5%99%A8.html", "category": "VPS", "tags": []}, {"title": "前言 | 一键虚拟化项目", "url": "https://www.spiritlhl.net/guide/pve/pve_precheck.html", "category": "VPS", "tags": []}, {"title": "一个真实地址生成器", "url": "https://www.nodeseek.com/post-320975-1", "category": "VPS", "tags": []}, {"title": "拒绝流量账单，利用哪吒面板来防止你的甲骨文、AWS等主机流量用超。 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/233349/15", "category": "VPS", "tags": ["Linux"]}, {"title": "🚀 使用 ClawCloud 部署 Sub-Store - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/593778", "category": "VPS", "tags": ["Linux"]}, {"title": "SJSU复活， San Jose State University， 权益汇总 - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/592434", "category": "VPS", "tags": ["Linux"]}, {"title": "Sub Store 部署 - Lᴜᴄʏ's Tool", "url": "https://wiki.repcz.link/substore/install/", "category": "VPS", "tags": []}, {"title": "YJesus/Unhide: Stable version of Unhide", "url": "https://github.com/YJesus/Unhide", "category": "VPS", "tags": ["GitHub"]}, {"title": "项目发布一天内破270+star 开心死了 发个帖先 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/604338/2", "category": "VPS", "tags": ["Linux"]}, {"title": "分享自己开发的开源指纹浏览器 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/611344/17", "category": "VPS", "tags": ["开源", "Linux"]}, {"title": "使用 Rclone 挂载 OneDrive 自动备份 VPS 数据 - 我是怪兽", "url": "https://guaishoux.com/339.html", "category": "VPS", "tags": []}, {"title": "教育部学籍在线验证报告编辑器", "url": "https://edu.yun7.de/", "category": "VPS", "tags": []}, {"title": "ASU拿下Cursor Pro，相关步骤可以参考 - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/625491", "category": "VPS", "tags": ["Linux"]}, {"title": "分享1panel无代码部署qbittorrent+vertex进行PT刷流，把吃灰小鸡利用起来", "url": "https://www.nodeseek.com/post-305107-1", "category": "VPS", "tags": []}, {"title": "\"订阅\" - 猫猫博客", "url": "https://catcat.blog/?s=%E8%AE%A2%E9%98%85", "category": "VPS", "tags": []}, {"title": "https://github.com/google-gemini/gemini-cli", "url": "https://github.com/google-gemini/gemini-cli", "category": "VPS", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "Gemini 抓狂了 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/751070/4", "category": "VPS", "tags": ["Linux"]}, {"title": "我用的微信，关 clash 什么事？ - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/749281/4", "category": "VPS", "tags": ["Linux"]}, {"title": "网络代理工具箱", "url": "https://www.haitunt.org/app.html", "category": "VPS", "tags": ["工具"]}, {"title": "XTLS/RealiTLScanner: A TLS server scanner for Reality", "url": "https://github.com/XTLS/RealiTLScanner", "category": "VPS", "tags": ["GitHub"]}, {"title": "Debian/Ubuntu 中安装和配置 UFW（简单防火墙） - P3TERX ZONE", "url": "https://p3terx.com/archives/installing-and-configuring-ufw-in-debian.html", "category": "VPS", "tags": []}, {"title": "如何在 Debian 12 Linux 中向 sudo 组添加用户 - LinuxStory", "url": "https://linuxstory.org/how-to-add-a-user-to-sudo-group-in-debian-12-linux/", "category": "VPS", "tags": []}, {"title": "Unraid 篇三：导航页HomeLab全网最全测评_软件应用_什么值得买", "url": "https://post.smzdm.com/p/amxkop7p/", "category": "unraid", "tags": ["Ai"]}, {"title": "unRAID Server Pro 6.11.5 скачать бесплатно. 70", "url": "https://softoroom.org/topic89043.html?pid=595807&st=70&#entry595807", "category": "unraid", "tags": ["Ai"]}, {"title": "[亲测可用]Unraid开心版6.10.3与安装教程 - 盒子萌", "url": "https://www.boxmoe.com/642.html", "category": "unraid", "tags": ["Ai", "教程"]}, {"title": "unraid下格式化硬盘 | 刘学馆 | nas相关笔记", "url": "http://www.x86.icu:6011/unraid/128.html", "category": "unraid", "tags": ["Ai"]}, {"title": "求助！win11 wm 打开原神 显示虚拟机下无法运行 - Chinese / 简体中文 - Unraid", "url": "https://forums.unraid.net/topic/126098-%E6%B1%82%E5%8A%A9%EF%BC%81win11-wm-%E6%89%93%E5%BC%80%E5%8E%9F%E7%A5%9E-%E6%98%BE%E7%A4%BA%E8%99%9A%E6%8B%9F%E6%9C%BA%E4%B8%8B%E6%97%A0%E6%B3%95%E8%BF%90%E8%A1%8C/", "category": "unraid", "tags": ["Ai"]}, {"title": "UNRAID虚拟群晖918 6.2.3教程 - Tank 米多贝克", "url": "https://www.mi-d.cn/847", "category": "unraid", "tags": ["Ai", "教程"]}, {"title": "家用媒体服务器NAS 使用UNRAID系统的正确的玩法！直通网卡、直通硬盘、挂载群晖虚拟机文件！_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/awx0nglg/", "category": "unraid", "tags": ["Ai"]}, {"title": "unRaid 直通Win10显卡GT710 ，代码43 - Chinese / 简体中文 - Unraid", "url": "https://forums.unraid.net/topic/121428-unraid-%E7%9B%B4%E9%80%9Awin10%E6%98%BE%E5%8D%A1gt710-%EF%BC%8C%E4%BB%A3%E7%A0%8143/", "category": "unraid", "tags": ["Ai"]}, {"title": "unraid 自定义主题 CSS | 🌧️ 栗山未来", "url": "https://lswl.in/2022/04/23/unraid-theme-custom-css/", "category": "unraid", "tags": ["Ai"]}, {"title": "UNRAID一篇就够！安装黑苹果macOS_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/axlzlnp3/", "category": "unraid", "tags": ["Ai"]}, {"title": "Unraid安装群晖DS918+7测试硬解和人脸识别 - 易波叶平", "url": "https://zhaouncle.com/unraid%E5%AE%89%E8%A3%85%E7%BE%A4%E6%99%96ds918-7%E6%B5%8B%E8%AF%95%E7%A1%AC%E8%A7%A3%E5%92%8C%E4%BA%BA%E8%84%B8%E8%AF%86%E5%88%AB/#33-%E5%9B%A0%E4%B8%BA-gpu-%E7%9A%84%E4%BD%8D%E7%BD%AE%E9%97%AE%E9%A2%98%E9%9C%80%E8%A6%81%E4%BF%AE%E6%94%B9%E4%BB%96%E7%9A%84%E6%80%BB%E7%BA%BF%E4%BD%8D%E7%BD%AE%E6%94%B9%E6%88%90%E7%BA%A2%E6%A1%86%E5%86%85%E7%9A%84%E6%A0%B7%E5%AD%90bus0x00-slot0x02%E5%90%A6%E5%88%99%E4%BC%9A%E5%AF%BC%E8%87%B4%E6%A0%B8%E6%98%BE%E8%AF%86%E5%88%AB%E6%9C%89%E9%97%AE%E9%A2%98", "category": "unraid", "tags": ["Ai"]}, {"title": "搞定unraid直通核显给WIN10后黑屏、声卡无输出问题", "url": "http://www.360doc.com/content/23/0119/16/4703094_1064214122.shtml", "category": "unraid", "tags": ["Ai"]}, {"title": "Unraid 篇一：Docker核显硬解 + windows虚拟机外接显示器物理输出iGPU YES!_显示器_什么值得买", "url": "https://post.smzdm.com/p/apv7l397/", "category": "unraid", "tags": ["Ai"]}, {"title": "nas 篇一：unraid折腾之win10直通“唯一”独立显卡", "url": "http://www.360doc.com/content/23/0119/15/4703094_1064212787.shtml", "category": "unraid", "tags": ["Ai"]}, {"title": "NVIDIA 驱动和 CUDA 版本信息速查 // 杰哥的{运维,编程,调板子}小笔记", "url": "https://jia.je/software/2021/12/26/nvidia-cuda/", "category": "unraid", "tags": []}, {"title": "Advanced GPU passthrough techniques on Unraid - YouTube", "url": "https://www.youtube.com/watch?v=QlTVANDndpM", "category": "unraid", "tags": ["Ai"]}, {"title": "硬件笔记之GP106-90 3GB GTX1060 3GB魔改 - 腾讯云开发者社区-腾讯云", "url": "https://cloud.tencent.com/developer/article/1675546", "category": "unraid", "tags": []}, {"title": "Tesla M40 vGPU Proxmox 7.1 | ZemaToxic's Dev Blog", "url": "https://blog.zematoxic.com/06/03/2022/Tesla-M40-vGPU-Proxmox-7-1/", "category": "unraid", "tags": []}, {"title": "Proxmox VE直通N卡Code43解决 - hello world!", "url": "https://echo.gg/2022/03/343/#post-images", "category": "unraid", "tags": []}, {"title": "Tesla M40 使用分享_tesla m40 双卡_MarineCode的博客-CSDN博客", "url": "https://blog.csdn.net/m0_47455189/article/details/126432198", "category": "unraid", "tags": []}, {"title": "《ai绘画》最全stable diffusion安装教程 所有软件安装和指令 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv22011634?from=articleDetail", "category": "unraid", "tags": ["Ai", "教程"]}, {"title": "群晖全新部署傻妞 对接芝士、公众号完整教程-牧之笔记 - 世界不应有局限", "url": "https://www.mspace.cc/archives/511", "category": "NAS", "tags": ["教程"]}, {"title": "Mikan Project - 我的番组", "url": "https://mikanani.me/Home/MyBangumi", "category": "NAS", "tags": []}, {"title": "arpl編譯下載速度過慢？修改兩個文件拯救你的下載速度", "url": "https://inewsdb.com/%e6%95%b8%e7%a2%bc/arpl%e7%b7%a8%e8%ad%af%e4%b8%8b%e8%bc%89%e9%80%9f%e5%ba%a6%e9%81%8e%e6%85%a2%ef%bc%9f%e4%bf%ae%e6%94%b9%e5%85%a9%e5%80%8b%e6%96%87%e4%bb%b6%e6%8b%af%e6%95%91%e4%bd%a0%e7%9a%84%e4%b8%8b%e8%bc%89/", "category": "NAS", "tags": []}, {"title": "群晖DSM 7使用Docker安装ZeroTier", "url": "https://www.itblogcn.com/article/1818.html", "category": "NAS", "tags": []}, {"title": "群晖算号器(全洗白)", "url": "http://api.ssr0.cn:8000/sn?model=DS918&sn=17A0PDN226500&mac=001132829B71", "category": "NAS", "tags": []}, {"title": "群晖Docker：小白安装tmm刮削保姆级教程，修改host解决刮削不全 ，建立完美电影墙！_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/ar0nq5dg/", "category": "NAS", "tags": ["教程"]}, {"title": "群晖上通过RcloneBrowser挂载云盘 | 老苏的blog", "url": "https://laosu.ml/2021/06/21/%E7%BE%A4%E6%99%96%E4%B8%8A%E9%80%9A%E8%BF%87RcloneBrowser%E6%8C%82%E8%BD%BD%E4%BA%91%E7%9B%98/", "category": "NAS", "tags": []}, {"title": "《群晖》Rclone 安装配置教程 - 连接 OneDrive - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv16130034?from=search", "category": "NAS", "tags": ["教程"]}, {"title": "将阿里云盘挂载为Webdav并使用rclone挂载到本地 – 南猫", "url": "https://southcat.net/2811.html", "category": "NAS", "tags": []}, {"title": "Alist使用Cloudflare workers为OneDrive加速 - fl0w1nd‘s blog", "url": "https://blog.itleaf.xyz/technology/8.html", "category": "NAS", "tags": []}, {"title": "群晖DSM7.0使用zerotier套件的简单办法 - 我不是矿神", "url": "https://imnks.com/3175.html", "category": "NAS", "tags": []}, {"title": "群晖6.1+6.2 docker开启局域网桥接并创建过滤广告容器-云深不知处", "url": "https://wps.520810.xyz:666/?p=752", "category": "NAS", "tags": []}, {"title": "群晖必备的一些套件安装 - 科技玩家", "url": "https://www.kejiwanjia.com/jiaocheng/zheteng/notes/42781.html", "category": "NAS", "tags": []}, {"title": "群辉DSM7.0.1安装bootstrap后解决wget: error while loading shared libraries: libgnuintl.so.8: cannot open shared object file: No such file or directory - 科技玩家", "url": "https://www.kejiwanjia.com/jiaocheng/62293.html", "category": "NAS", "tags": []}, {"title": "estrellaxd/auto_bangumi - Docker Image | Docker Hub", "url": "https://hub.docker.com/r/estrellaxd/auto_bangumi", "category": "NAS", "tags": []}, {"title": "GXNAS博客 - https://wp.gxnas.com", "url": "https://wp.gxnas.com/", "category": "NAS", "tags": []}, {"title": "NAS备忘录 篇三十九：UNRAID 6.11 虚拟机安装 WIN 10，并虚拟化核显（十代 QSRL CPU）_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/a7nkr2po/", "category": "NAS", "tags": ["Ai"]}, {"title": "UNRAID 6.11 虚拟机安装 WIN 10，并虚拟化核显（十代 QSRL CPU）|插件|英特尔|win10|unraid|cpu_网易订阅", "url": "https://www.163.com/dy/article/HMO3ET0G053186JB.html", "category": "NAS", "tags": ["Ai"]}, {"title": "Unraid 篇一：Unraid 安装群晖 DS918+7 测试硬解和人脸识别_NAS存储_什么值得买", "url": "https://post.smzdm.com/p/a5dl2808/", "category": "NAS", "tags": ["Ai"]}, {"title": "P106解锁版驱动一键安装包（更新）【p106吧】_百度贴吧", "url": "https://tieba.baidu.com/p/8178312322", "category": "NAS", "tags": []}, {"title": "Win11系统安装过程中如何跳过网络连接？_哔哩哔哩_bilibili", "url": "https://www.bilibili.com/video/BV13G411E7HQ/?spm_id_from=333.788.recommend_more_video.12&vd_source=0730c911268ff523fed5714b68099a2d", "category": "NAS", "tags": []}, {"title": "佛西博客", "url": "https://foxi.buduanwang.vip/", "category": "NAS", "tags": []}, {"title": "vGPU教程 - 国光的 PVE 环境搭建教程", "url": "https://pve.sqlsec.com/4/5/#_11", "category": "NAS", "tags": ["教程"]}, {"title": "Tesla M40 训练机组装与散热改造_里先森的博客-CSDN博客", "url": "https://blog.csdn.net/sements/article/details/125256812?ops_request_misc=%257B%2522request%255Fid%2522%253A%2522166531170816800182740292%2522%252C%2522scm%2522%253A%252220140713.130102334%E2%80%A6%2522%257D&request_id=166531170816800182740292&biz_id=0&utm_medium=distribute.pc_search_result.none-task-blog-2allsobaiduend~default-1-125256812-null-null.142v52pc_rank_34_1,201v3control_1&utm_term=tesla%20m40&spm=1018.2226.3001.4187", "category": "NAS", "tags": []}, {"title": "NVIDIA TESLA M40 24G的奇妙游戏之旅 – Fantasy Land", "url": "https://east.moe/archives/1264", "category": "NAS", "tags": []}, {"title": "Activating a Secondary Display on Windows 10 when no Monitor is Connected - Amyuni Technologies", "url": "https://www.amyuni.com/forum/viewtopic.php?t=3030", "category": "NAS", "tags": []}, {"title": "Tesla P40 计算卡 与 GTX750 亮机卡 宠粉远程服务实录 已授权录制_哔哩哔哩_bilibili", "url": "https://www.bilibili.com/video/BV13W4y1s7so/?spm_id_from=333.788.recommend_more_video.14&vd_source=0730c911268ff523fed5714b68099a2d", "category": "NAS", "tags": []}, {"title": "PVE安装Win10并直通显卡(独显)", "url": "https://iamroot.cn/pvean-zhuang-win/", "category": "NAS", "tags": []}, {"title": "i 硬件直通及显卡直通配置 · ivanhao/pvetools Wiki · GitHub", "url": "https://github.com/ivanhao/pvetools/wiki/i-%E7%A1%AC%E4%BB%B6%E7%9B%B4%E9%80%9A%E5%8F%8A%E6%98%BE%E5%8D%A1%E7%9B%B4%E9%80%9A%E9%85%8D%E7%BD%AE", "category": "NAS", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "Proxmox VE (Tesla P40) vGPU 配置 - azhuge233's", "url": "https://azhuge233.com/proxmox-ve-tesla-p40-vgpu-%e9%85%8d%e7%bd%ae/", "category": "NAS", "tags": []}, {"title": "Proxmox PVE ISO镜像所有版本下载 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv21158501", "category": "NAS", "tags": []}, {"title": "Proxmox VE（PVE）中黑群晖DSM7.X引导编译与安装 - 佐蓝小站", "url": "https://blog.gurenkai.com:11443/archives/install-pve-dsm", "category": "NAS", "tags": []}, {"title": "PVE系列教程(十四)、安装黑苹果最新系统MacOS Monterey(BigSur、Mont", "url": "https://weibo.com/ttarticle/p/show?id=2309404877088791593035#_loginLayer_1680108137779", "category": "NAS", "tags": ["教程"]}, {"title": "PVE 虚拟化黑苹果显卡直通及远程访问教程", "url": "https://blog.lv5.moe/p/pve-virtualized-hackintosh-gpu-passthrough-and-remote-access-tutorial#%E5%88%86%E9%85%8D%E6%98%BE%E5%8D%A1", "category": "NAS", "tags": ["教程"]}, {"title": "手把手基于Proxmox VE安装macOS Monterey【黑苹果】 - 掘金", "url": "https://juejin.cn/post/7158442404188520484", "category": "NAS", "tags": []}, {"title": "青龙面板最全依赖2022-3-31更新-CSDN博客", "url": "https://blog.csdn.net/Lihuos/article/details/122869207", "category": "NAS", "tags": []}, {"title": "Proxmox VE 7.1安装macOS 10.15及GPU穿通方案 - 分贝网", "url": "https://db.ci/daily/5293.html", "category": "NAS", "tags": []}, {"title": "| APQA镜像站", "url": "https://mirrors.apqa.cn/proxmox-edge/officialiso/", "category": "NAS", "tags": []}, {"title": "佛西博客 - pve虚拟机VGPU方案简析", "url": "https://foxi.buduanwang.vip/vdi/573.html/", "category": "NAS", "tags": []}, {"title": "PolloLoco / NVIDIA vGPU Guide · GitLab", "url": "https://gitlab.com/polloloco/vgpu-proxmox", "category": "NAS", "tags": []}, {"title": "vGPU教程 - 国光的 PVE 环境搭建教程", "url": "https://pve.sqlsec.com/4/5/", "category": "NAS", "tags": ["教程"]}, {"title": "Release NVIDIA vGPU Drivers 14.4 · justin-himself/NVIDIA-VGPU-Driver-Archive", "url": "https://github.com/justin-himself/NVIDIA-VGPU-Driver-Archive/releases/tag/14.4", "category": "NAS", "tags": ["GitHub"]}, {"title": "collinwebdesigns/fastapi-dls - Docker Image | Docker Hub", "url": "https://hub.docker.com/r/collinwebdesigns/fastapi-dls", "category": "NAS", "tags": ["Api"]}, {"title": "国光的 PVE 生产环境配置优化记录 | 国光", "url": "https://www.sqlsec.com/2022/04/pve.html#PVE-%E9%BB%91%E8%8B%B9%E6%9E%9C%EF%BC%88%E4%B8%8A%EF%BC%89", "category": "NAS", "tags": []}, {"title": "群晖 Docker 搭建 Clash 订阅转换平台教程-牧之笔记 - 世界不应有局限", "url": "https://www.mspace.cc/archives/373", "category": "NAS", "tags": ["教程"]}, {"title": "RDP 优化操作 微软远程桌面 开启显卡加速、60FPS、USB设备重定向 | 橙叶博客", "url": "https://www.orgleaf.com/3771.html", "category": "NAS", "tags": []}, {"title": "不同版本cuda对应的NVIDIA驱动版本_cuda 驱动版本对应_sundaygeek的博客-CSDN博客", "url": "https://blog.csdn.net/mouse1598189/article/details/86695400", "category": "NAS", "tags": []}, {"title": "PVE中如何设置对虚拟机进行去虚拟化 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv22361926/", "category": "NAS", "tags": []}, {"title": "NVIDIA 英伟达TESLA计算卡专用驱动 GRID驱动下载链接分享（P40 P100 M40等显卡可用） - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv19405017", "category": "NAS", "tags": []}, {"title": "Linux上如何使用Stable Diffusion WebUI - 掘金", "url": "https://juejin.cn/post/7208946311886372924", "category": "NAS", "tags": []}, {"title": "Proxmox VE(PVE)虚拟机绕过软件的虚拟机检查来玩原神 - 某咸鱼的笔记", "url": "https://www.wunote.cn/article/4801/", "category": "NAS", "tags": []}, {"title": "ProxmoxVE安装VGPU – 心野小站", "url": "https://www.xyxb.vip/?p=380", "category": "NAS", "tags": []}, {"title": "Linux服务器安装cuda,cudnn，显卡驱动和pytorch超详细流程_kingfoulin的博客-CSDN博客", "url": "https://blog.csdn.net/kingfoulin/article/details/98872965", "category": "NAS", "tags": []}, {"title": "2023黑苹果Big Sur/Monterey免驱原生支持独立显卡购买指南 | 1GPU", "url": "https://www.1gpu.com/553.html", "category": "NAS", "tags": []}, {"title": "alist.nn.ci/zh/guide/", "url": "https://alist.nn.ci/zh/guide/", "category": "NAS", "tags": []}, {"title": "巧用docker里的ubuntu，跑傻妞、oicq (onebot:再见👋🏻) | 梦磊の博客", "url": "https://ymlclub.cn/2021121119419/", "category": "NAS", "tags": []}, {"title": "Synology 群晖 Nvidia vGPU/GPU 实践指南-蔚然小站", "url": "https://blog.kkk.rs/archives/12#%E5%AE%89%E8%A3%85%E8%BF%87%E7%A8%8B", "category": "NAS", "tags": []}, {"title": "List of working motherboards · Issue #11 · xCuri0/ReBarUEFI", "url": "https://github.com/xCuri0/ReBarUEFI/issues/11", "category": "NAS", "tags": ["GitHub"]}, {"title": "群晖算号器(全洗白)", "url": "http://api.ssr0.cn:8000/sn?model=DS918&sn=17A0PDN319001&mac=00113282A3FB", "category": "NAS", "tags": []}, {"title": "vGPU在Proxmox VE下的配置与使用 – 雪林荧光", "url": "https://xinalin.com/159/vgpu-configuration-on-pve", "category": "NAS", "tags": []}, {"title": "nVidia PCI id database — envytools git documentation", "url": "https://envytools.readthedocs.io/en/latest/hw/pciid.html#nv10", "category": "NAS", "tags": []}, {"title": "我有一个机器人，可骂街、舔狗、土味、发扔子 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv21285551/", "category": "NAS", "tags": []}, {"title": "硬件直通 - 国光的 PVE 环境搭建教程", "url": "https://pve.sqlsec.com/4/2/", "category": "NAS", "tags": ["教程"]}, {"title": "pve7 pve8 kvmqemu反虚拟化检测显卡直通玩游戏教程小白直接安装+大神可以自己源码编译 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv26245305/", "category": "NAS", "tags": ["教程"]}, {"title": "PVE开启硬件直通功能", "url": "http://linux.it.net.cn/e/Virtualization/Proxmox/2023/0306/31611.html", "category": "NAS", "tags": []}, {"title": "APQA网盘 - /vGPU/17.0/", "url": "https://foxi.buduanwang.vip/pan/vGPU/17.0/", "category": "NAS", "tags": []}, {"title": "Releases · RROrg/rr", "url": "https://github.com/RROrg/rr/releases", "category": "NAS", "tags": ["GitHub"]}, {"title": "解决---设备“VMnet0”上的网桥没有运行。该虚拟机无法与此主机或网络上的其他主机进行通信。 无法连接虚拟设备“Ethernet0”。-CSDN博客", "url": "https://blog.csdn.net/Wysnbb/article/details/123996480", "category": "NAS", "tags": []}, {"title": "alist-tvbox/doc/README_zh.md at master · power721/alist-tvbox · GitHub", "url": "https://github.com/power721/alist-tvbox/blob/master/doc/README_zh.md", "category": "NAS", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "带8个免费监控授权、支持独显硬解的黑群晖DSM7.X，想要不？ - GXNAS博客", "url": "https://wp.gxnas.com/12534.html", "category": "NAS", "tags": []}, {"title": "使用VirtualBox虚拟机安装群晖7.1系统 | 牧尘的NAS小站", "url": "https://www.dreamlyn.cn/1751037144.html", "category": "NAS", "tags": []}, {"title": "让VirtualBox虚拟机实现开机自动后台运行 | 四少爷的blog", "url": "https://www.jermey.cn/2021/06/05/1.html", "category": "NAS", "tags": []}, {"title": "TANK电玩 公开群晖半洗白码 - 米多贝克&米多网络工程", "url": "https://mi-d.cn/4430", "category": "NAS", "tags": []}, {"title": "VMware虚拟机安装黑群晖系统笔记_林鸿风采的技术博客_51CTO博客", "url": "https://blog.51cto.com/linhong/10445812", "category": "NAS", "tags": []}, {"title": "VMware安装Ubuntu(2024最新最全版)-CSDN博客", "url": "https://blog.csdn.net/fanyun_01/article/details/136540798", "category": "NAS", "tags": []}, {"title": "佛西博客 - 来自民间的VGPU授权fastapi-dls", "url": "https://foxi.buduanwang.vip/virtualization/pve/2195.html/", "category": "NAS", "tags": ["Api"]}, {"title": "显卡虚拟化，Tesla P4在PVE8下的vgpu配置方案，兼容多显卡直通 - 哔哩哔哩", "url": "https://www.bilibili.com/read/cv33513142/", "category": "NAS", "tags": []}, {"title": "文件 · driver-550.90.05 · <PERSON><PERSON><PERSON><PERSON> / ELKH Cloud · GitLab", "url": "https://gitlab.wigner.hu/pinter.adam/elkh-cloud/-/tree/driver-550.90.05?ref_type=heads", "category": "NAS", "tags": []}, {"title": "virtio-win/virtio-win-pkg-scripts: Scripts for packaging virtio-win drivers", "url": "https://github.com/virtio-win/virtio-win-pkg-scripts?tab=readme-ov-file", "category": "NAS", "tags": ["GitHub"]}, {"title": "windows上安装miniforge和jupyterlab_miniforge windows-CSDN博客", "url": "https://blog.csdn.net/<PERSON><PERSON><PERSON>_<PERSON>/article/details/139124165", "category": "NAS", "tags": []}, {"title": "How to run a jupyter notebook through a remote server on local machine? - Stack Overflow", "url": "https://stackoverflow.com/questions/69244218/how-to-run-a-jupyter-notebook-through-a-remote-server-on-local-machine", "category": "NAS", "tags": []}, {"title": "驱动和其他镜像 | AList", "url": "https://index.mitsea.com/%E8%BD%AF%E4%BB%B6/%E9%A9%B1%E5%8A%A8%E5%92%8C%E5%85%B6%E4%BB%96%E9%95%9C%E5%83%8F", "category": "NAS", "tags": []}, {"title": "适用于 NVIDIA RTX 虚拟工作站 (vWS) 的驱动程序  |  Compute Engine Documentation  |  Google Cloud", "url": "https://cloud.google.com/compute/docs/gpus/grid-drivers-table?hl=zh-cn", "category": "NAS", "tags": []}, {"title": "[Android] ubuntu虚拟机上搭建 Waydroid 环境-CSDN博客", "url": "https://blog.csdn.net/ykun089/article/details/135049480", "category": "NAS", "tags": []}, {"title": "解决Proxmox VE虚拟机无法运行原神一例 | Bug侠", "url": "https://bugxia.com/3600.html", "category": "NAS", "tags": []}, {"title": "No.1 简单搭建 Zerotier Moon 为虚拟网络加速 | tvtv.fun", "url": "https://tvtv.fun/vps/001.html", "category": "NAS", "tags": []}, {"title": "July's云盘", "url": "https://yun.yangwenqing.com/ESXI_PVE", "category": "NAS", "tags": []}, {"title": "2024最新PVE安装黑群晖系统教程 - 折腾笔记", "url": "https://blog.zwbcc.cn/archives/1710522887031", "category": "NAS", "tags": ["教程"]}, {"title": "pve-anti-detection/README.md at main · lixiaoliu666/pve-anti-detection · GitHub", "url": "https://github.com/lixiaoliu666/pve-anti-detection/blob/main/README.md", "category": "NAS", "tags": ["Ai", "<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "Proxmox VE 8.1 vGPU 配置 （A6000）", "url": "https://blog.mitsea.com/d29bb28b14984443b232263348b946ba/", "category": "NAS", "tags": []}, {"title": "PVE直通显卡 & Intel SRIOV - MAENE - 博客园", "url": "https://www.cnblogs.com/MAENESA/p/18005241", "category": "NAS", "tags": []}, {"title": "Proxmox VE直通硬盘（全盘映射方式） | 自说Me话", "url": "https://isay.me/2024/04/pve-harddisk-passthrough.html", "category": "NAS", "tags": []}, {"title": "佛西博客 - Proxmox VE pve硬盘直通", "url": "https://foxi.buduanwang.vip/virtualization/1754.html/", "category": "NAS", "tags": []}, {"title": "总结几点Wake On Lan(WOL)失败的原因 · Issue #124 · Bpazy/blog", "url": "https://github.com/Bpazy/blog/issues/124", "category": "NAS", "tags": ["GitHub"]}, {"title": "NVIDIA 驱动和 CUDA 版本信息速查 - 杰哥的{运维，编程，调板子}小笔记", "url": "https://jia.je/software/2021/12/26/nvidia-cuda/#%E5%B8%B8%E7%94%A8%E5%9C%B0%E5%9D%80", "category": "NAS", "tags": []}, {"title": "【保姆级教程】Windows安装CUDA及cuDNN_windows安装cudnn-CSDN博客", "url": "https://blog.csdn.net/qq_40968179/article/details/128996692", "category": "NAS", "tags": ["教程"]}, {"title": "Host_Drivers | 小陈折腾日记的网盘", "url": "https://alist.geekxw.top/NVIDIA-GRID-Linux-KVM-550.144.02-550.144.03-553.62/Host_Drivers", "category": "NAS", "tags": []}, {"title": "immortalwrt-mt798x项目介绍", "url": "https://cmi.hanwckf.top/p/immortalwrt-mt798x/", "category": "路由", "tags": []}, {"title": "OpenWrt 固件自编译教程：从入门到酸爽！ - 喵斯基部落", "url": "https://www.moewah.com/archives/4003.html", "category": "路由", "tags": ["教程"]}, {"title": "GitHub - kenzok8/openwrt-packages: openwrt常用软件包", "url": "https://github.com/kenzok8/openwrt-packages", "category": "路由", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "零基础编译OpenWrt看这一篇就够了 – 沫延说", "url": "https://blog.topstalk.com/%E9%9B%B6%E5%9F%BA%E7%A1%80%E7%BC%96%E8%AF%91openwrt%E7%9C%8B%E8%BF%99%E4%B8%80%E7%AF%87%E5%B0%B1%E5%A4%9F%E4%BA%86/", "category": "路由", "tags": []}, {"title": "[OpenClash+AdGuardHome]的正确使用姿势/去广告/防污/加速解析-软路由,x86系统,openwrt(x86),Router OS 等-恩山无线论坛 - Powered by Discuz!", "url": "https://www.right.com.cn/forum/thread-4486232-1-1.html", "category": "路由", "tags": []}, {"title": "AdGuard Home 用户名与密码是多少-软路由,x86系统,openwrt(x86),Router OS 等-恩山无线论坛 - Powered by Discuz!", "url": "https://www.right.com.cn/FORUM/thread-4037690-1-1.html", "category": "路由", "tags": []}, {"title": "Сайт с рекламой для проверки блокировщиков рекламы: AdBlock, AdBlock Plus, AdGuard, Ghostery…", "url": "https://checkadblock.ru/", "category": "路由", "tags": []}, {"title": "UnblockNeteaseMusic/luci-app-unblockneteasemusic at master", "url": "https://github.com/UnblockNeteaseMusic/luci-app-unblockneteasemusic/tree/master", "category": "路由", "tags": ["GitHub"]}, {"title": "MartialBE/luci-app-mosdns: 一个 DNS 转发器 - OpenWRT", "url": "https://github.com/MartialBE/luci-app-mosdns", "category": "路由", "tags": ["GitHub"]}, {"title": "<PERSON><PERSON>", "url": "https://opt.cn2qq.com/padavan/", "category": "路由", "tags": []}, {"title": "sagehou/360T7-ImmortalWrt: Custom ImmortalWrt image for 360T7(mt7981)", "url": "https://github.com/sagehou/360T7-ImmortalWrt", "category": "路由", "tags": ["GitHub"]}, {"title": "烽火固件全家桶 | CPEManager官方仓库", "url": "http://pan.163wx.cn/%E7%83%BD%E7%81%AB%E5%9B%BA%E4%BB%B6%E5%85%A8%E5%AE%B6%E6%A1%B6", "category": "路由", "tags": []}, {"title": "OpenWrt软路由固件下载与在线定制编译", "url": "https://openwrt.ai/?target=mediatek%2Ffilogic&id=qihoo_360t7", "category": "路由", "tags": []}, {"title": "ImmortalWrt Firmware Selector", "url": "https://firmware-selector.immortalwrt.org/", "category": "路由", "tags": []}, {"title": "immortalwrt/homeproxy: The modern ImmortalWrt proxy platform for ARM64/AMD64 (powered by sing-box)", "url": "https://github.com/immortalwrt/homeproxy", "category": "路由", "tags": ["GitHub"]}, {"title": "OpenClash 设置教程 · Aethersailor/Custom_OpenClash_Rules Wiki", "url": "https://github.com/Aethersailor/Custom_OpenClash_Rules/wiki/OpenClash-%E8%AE%BE%E7%BD%AE%E6%95%99%E7%A8%8B", "category": "路由", "tags": ["Ai", "教程", "GitHub"]}, {"title": "【瑞士军刀】放弃fakeip，拥抱realip，最强网络代理工具sing-box新手指南，从此不知DNS泄漏/DNS污染为何物，软路由插件homeproxy，奈飞DNS解锁、sniff流量嗅探覆写解析 - YouTube", "url": "https://www.youtube.com/watch?v=BAfbkLizFGc&embeds_referring_euri=https%3A%2F%2Fwww.bulianglin.com%2F&source_ve_path=OTY3MTQ", "category": "路由", "tags": ["工具"]}, {"title": "OpenWrt 编译步骤与命令详解 - 阿风小子 - 博客园", "url": "https://www.cnblogs.com/kn-zheng/p/17340776.html", "category": "路由", "tags": []}, {"title": "对OpenWrt的根分区和系统文件进行扩容 – 你猜的技术分享", "url": "https://www.youguess.site/index.php/2024/01/23/20/28/38/98/", "category": "路由", "tags": []}, {"title": "编译教程 | All about X-Wrt", "url": "https://blog.x-wrt.com/docs/build/", "category": "路由", "tags": ["教程"]}, {"title": "校园网防止多设备检测指北 · 瞳のBlog", "url": "https://hetong-re4per.com/posts/multi-device-detection/", "category": "路由", "tags": []}, {"title": "OpenWRT安装WireGuard实现内网穿透 | HomeLab Dolingou", "url": "https://www.dolingou.com/article/openwrt-install-wireguard-for-nat-traversal", "category": "路由", "tags": []}, {"title": "使用 OpenWrt 24.10.1 官网源码编译固件", "url": "https://maxqiu.com/article/detail/153", "category": "路由", "tags": []}, {"title": "OpenClash 设置方案 · Aethersailor/Custom_OpenClash_Rules Wiki", "url": "https://github.com/Aethersailor/Custom_OpenClash_Rules/wiki/OpenClash-%E8%AE%BE%E7%BD%AE%E6%96%B9%E6%A1%88#01-%E5%85%B3%E4%BA%8E%E6%8E%A8%E8%8D%90%E5%9B%BA%E4%BB%B6", "category": "路由", "tags": ["Ai", "GitHub"]}, {"title": "OpenClash 因 GeoIP.dat 启动失败解决办法 - DivineEngine", "url": "https://divineengine.net/article/fix-openclash-geoip-dat-error/", "category": "路由", "tags": []}, {"title": "解决【/tmp/openclash_last_version】下载失败 - 沐沐言 ~ 个人博客", "url": "http://www.jintian.site/index.php/archives/41/", "category": "路由", "tags": []}, {"title": "Rat's Blog - 相逢的人会再相逢", "url": "https://www.moerats.com/", "category": "技术博客", "tags": []}, {"title": "非常论坛", "url": "https://machbbs.com/", "category": "技术博客", "tags": []}, {"title": "FDD（QL-Emotion）Linux搭建Docker版本图文教程", "url": "https://www.luomubiji.host/?p=898", "category": "技术博客", "tags": ["教程"]}, {"title": "E5 调用API续订服务：Microsoft 365 E5 Renew X_SundayRX的博客-CSDN博客_e5续订", "url": "https://blog.csdn.net/qq_33212020/article/details/119747634", "category": "技术博客", "tags": ["Api"]}, {"title": "Windows | 云梦", "url": "https://www.htcp.net/windows", "category": "技术博客", "tags": []}, {"title": "搭建Zerotier内网穿透网络及彻底删除zerotier方法_时间就好好的博客-CSDN博客_zerotier删除设备无法添加", "url": "https://blog.csdn.net/jsjason1/article/details/108844943", "category": "技术博客", "tags": []}, {"title": "win10 你不能访问此共享文件夹，因为你组织的安全策略..._排骨瘦肉丁的博客-CSDN博客_组织的安全策略阻止未经身份验证的来宾访问", "url": "https://blog.csdn.net/iamlihongwei/article/details/79377657", "category": "技术博客", "tags": []}, {"title": "AutoBangumi：自动追番，解放双手 - 初之音", "url": "https://www.himiku.com/archives/auto-bangumi.html", "category": "技术博客", "tags": []}, {"title": "Civitai", "url": "https://civitai.com/", "category": "AI", "tags": ["Ai"]}, {"title": "AutoDL-品质GPU租用平台-租GPU就上AutoDL", "url": "https://www.autodl.com/console/instance/list", "category": "AI", "tags": []}, {"title": "CUDA Toolkit Archive | NVIDIA Developer", "url": "https://developer.nvidia.com/cuda-toolkit-archive", "category": "AI", "tags": []}, {"title": "在Windows下正确地编译最新的pytorch和tensorflow_编译pytorch_纯洁的小火车的博客-CSDN博客", "url": "https://blog.csdn.net/weixin_42122722/article/details/122374308", "category": "AI", "tags": []}, {"title": "AI导航 - 优设AI导航 - 专业AIGC网站导航", "url": "https://hao.uisdc.com/ai/", "category": "AI", "tags": ["Ai"]}, {"title": "ddean2009/MoneyPrinterPlus: AI一键批量生成各类短视频,自动批量混剪短视频,自动把视频发布到抖音,快手,小红书,视频号上,赚钱从来没有这么容易过! 支持本地语音模型chatTTS,fasterwhisper,GPTSoVITS,支持云语音：Azure,阿里云,腾讯云。支持Stable diffusion,comfyUI直接AI生图。Generate short videos with one click using AI LLM,print money together! support:chatTTS,faster-whisper,GPTSoVITS,Azure,tencent Cloud,Ali Cloud.", "url": "https://github.com/ddean2009/MoneyPrinterPlus?tab=readme-ov-file#%E5%89%8D%E6%8F%90%E6%9D%A1%E4%BB%B6", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "WatermarkRemover/ at master · lxulxu/WatermarkRemover", "url": "https://github.com/lxulxu/WatermarkRemover/tree/master", "category": "AI", "tags": ["GitHub"]}, {"title": "Gemini 2.0 Flash Multimodal Live API Client", "url": "https://jokero-gem.deno.dev/", "category": "AI", "tags": ["Api"]}, {"title": "CodeWithGPU | 能复现才是好算法", "url": "https://www.codewithgpu.com/i/issuedetail/Akegarasu/lora-scripts/kohya-ss_flux/172635524838/0", "category": "AI", "tags": []}, {"title": "Calcium-Ion/new-api: AI模型接口管理与分发系统，支持将多种大模型转为OpenAI格式调用、支持Midjourney Proxy、<PERSON><PERSON>、<PERSON>rank，兼容易支付协议，可供个人或者企业内部管理与分发渠道使用，本项目基于One API二次开发。🍥 The next-generation LLM gateway and AI asset management system supports multiple languages.", "url": "https://github.com/Calcium-Ion/new-api", "category": "AI", "tags": ["Api", "Ai", "GitHub"]}, {"title": "做了个最快的B站直播录制、自动切片、自动渲染弹幕和字幕并投稿的项目，兼容 10 年前的机器！欢迎各位佬友使用并提建议。 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/325246/9", "category": "AI", "tags": ["Linux"]}, {"title": "让自动获取免费机场订阅更自动 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/93330", "category": "AI", "tags": ["Linux"]}, {"title": "新版宝宝教程 - Google 文档", "url": "https://docs.google.com/document/d/1AbhYWoEpgPg0PilaGIhKphdMOzhl_SVtaDhWlacH_Tg/edit?pli=1&tab=t.0#heading=h.g4xn5w7pauzg", "category": "AI", "tags": ["教程"]}, {"title": "做了一个自动切片的小工具，欢迎各位佬无门槛当上切片员，也欢迎提建议！ - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/515179/13", "category": "AI", "tags": ["工具", "Linux"]}, {"title": "【Cursor】可视化启动注册任务及账号管理 - 福利羊毛 / 福利羊毛, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/513320", "category": "AI", "tags": ["Linux"]}, {"title": "facebookresearch/xformers: Hackable and optimized Transformers building blocks, supporting a composable construction.", "url": "https://github.com/facebookresearch/xformers?tab=readme-ov-file", "category": "AI", "tags": ["GitHub"]}, {"title": "Tutorial (setup): Train Flux.1 Dev LoRAs using \"ComfyUI Flux Trainer\" : r/StableDiffusion", "url": "https://www.reddit.com/r/StableDiffusion/comments/1f5onyx/tutorial_setup_train_flux1_dev_loras_using/", "category": "AI", "tags": ["Ai"]}, {"title": "EvilBT/ComfyUI_SLK_joy_caption_two: ComfyUI Node", "url": "https://github.com/EvilBT/ComfyUI_SLK_joy_caption_two?tab=readme-ov-file", "category": "AI", "tags": ["GitHub"]}, {"title": "ComfyUI_SLK_joy_caption_two/examples/workflows.png at main · EvilBT/ComfyUI_SLK_joy_caption_two", "url": "https://github.com/EvilBT/ComfyUI_SLK_joy_caption_two/blob/main/examples/workflows.png", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "探索MCP-我的学习与实践笔记 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/504520", "category": "AI", "tags": ["Linux"]}, {"title": "【4/01 Apifox Mcp】个人使用 Cursor 一些调优和经验 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/523255/11", "category": "AI", "tags": ["Api", "Linux"]}, {"title": "awesome-mcp-servers/README-zh.md at main · punkpeye/awesome-mcp-servers", "url": "https://github.com/punkpeye/awesome-mcp-servers/blob/main/README-zh.md", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "Smithery - Model Context Protocol Registry", "url": "https://smithery.ai/?q=tiktok", "category": "AI", "tags": []}, {"title": "zhangyiming748/coze: 测试这个新的AI智能体", "url": "https://github.com/zhangyiming748/coze", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "zhtyyx/ioe: One-Stop Retail Inventory Solution", "url": "https://github.com/zhtyyx/ioe", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "xiaoti12/autodl_bot", "url": "https://github.com/xiaoti12/autodl_bot", "category": "AI", "tags": ["GitHub"]}, {"title": "ioe/README.docker_zh.md at main · zhtyyx/ioe", "url": "https://github.com/zhtyyx/ioe/blob/main/README.docker_zh.md", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "首页 | 类脑智识库 · ΓΝΩΣΗ WIKI", "url": "https://wiki.xn--35zx7g.org/", "category": "AI", "tags": []}, {"title": "Apps - miludeerforest | Modal", "url": "https://modal.com/apps/miludeerforest/main", "category": "AI", "tags": []}, {"title": "How to Train Multiple Flux LoRA Automatically on Free H100 GPUs with Just One Click - YouTube", "url": "https://www.youtube.com/watch?v=Xjuz92Xmv5w", "category": "AI", "tags": ["Ai"]}, {"title": "AINxtGen/ai-toolkit", "url": "https://github.com/AINxtGen/ai-toolkit", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "在 Free H100 GPU 上训练多个 Flux LoRA : r/comfyui --- Train Multiple Flux LoRA on Free H100 GPUs : r/comfyui", "url": "https://www.reddit.com/r/comfyui/comments/1ibgbvu/train_multiple_flux_lora_on_free_h100_gpus/", "category": "AI", "tags": ["Ai"]}, {"title": "AINxtGen/ai-toolkit", "url": "https://github.com/AINxtGen/ai-toolkit?tab=readme-ov-file", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "Ai动画77-乱入！多图融合视频一致性再突破！Wan2.1 Skyreels-A2预览版！Vace出现强大竞争对手！通义万象生态又出强大模型-Comfyui教程 - YouTube", "url": "https://www.youtube.com/watch?v=UvddiyCAf2k", "category": "AI", "tags": ["Ai", "教程"]}, {"title": "开源自荐|一个简洁、美观、实用的提示词管理网站。 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/282700", "category": "AI", "tags": ["开源", "Linux"]}, {"title": "SuperSMSBridge：基于 Telegram 超级群组的短信转发中间件 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/537557/4", "category": "AI", "tags": ["Linux"]}, {"title": "bestK/email_worker_parser: A simple fake email api", "url": "https://github.com/bestK/email_worker_parser", "category": "AI", "tags": ["Api", "Ai", "GitHub"]}, {"title": "clash脚本：增加了cursor相关的规则 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/542912/2", "category": "AI", "tags": ["Linux"]}, {"title": "Cursor 无限邮箱账号注册 - 开发调优 / 开发调优, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/541154", "category": "AI", "tags": ["Linux"]}, {"title": "轻量级的节点订阅转换工具：sublink-worker，支持 Sing-Box、Clash - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/193549", "category": "AI", "tags": ["工具", "Linux"]}, {"title": "学生包全能油猴脚本[自动定位 + 虚拟摄像头] - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/177723", "category": "AI", "tags": ["Linux"]}, {"title": "fff122/PicSpider: 批量爬取写真站,并展示", "url": "https://github.com/fff122/PicSpider", "category": "AI", "tags": ["GitHub"]}, {"title": "codelf/README_CN.md at main · Disdjj/codelf", "url": "https://github.com/Disdjj/codelf/blob/main/README_CN.md", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "yeongpin/cursor-free-vip: [Support 0.48.x]（Reset Cursor AI MachineID & Auto Sign Up / In & Bypass Higher Token Limit）自动注册 Cursor Ai ，自动重置机器ID ， 免费升级使用Pro功能: You've reached your trial request limit. / Too many free trial accounts used on this machine. Please upgrade to pro. We have this limit in place to prevent abuse. Please let us know if you believe this is a mistake.", "url": "https://github.com/yeongpin/cursor-free-vip", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "PoolHub", "url": "https://poolhub.me/", "category": "AI", "tags": []}, {"title": "简单易懂的现代魔法 - n8n 中文使用教程 | 页面找不到啦", "url": "https://n8n.akashio.com/welcome", "category": "AI", "tags": ["教程"]}, {"title": "【Cursor】Cursor 全局通用规则Rules V4.5 ：多维思考+五大规则模式 让你码到飞起 - 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/536898", "category": "AI", "tags": ["Linux"]}, {"title": "学生证生成器 (仅供内部测试使用)", "url": "https://student-id.pages.dev/", "category": "AI", "tags": []}, {"title": "TikTok Video Downloader | Download TikTok video without watermark - TikVid", "url": "https://tikvid.io/en", "category": "AI", "tags": ["Tiktok"]}, {"title": "VYNCX/F5-TTS-THAI: Text-to-Speech (TTS) 泰语 — 使用 Flow Matching 技术从文本生成语音的工具 --- VYNCX/F5-TTS-THAI: Text-to-Speech (TTS) ภาษาไทย — เครื่องมือสร้างเสียงพูดจากข้อความด้วยเทคนิค Flow Matching", "url": "https://github.com/VYNCX/F5-TTS-THAI", "category": "AI", "tags": ["Ai", "工具", "GitHub"]}, {"title": "飞书云文档", "url": "https://jexopm4t2a.feishu.cn/wiki/EjRPwux9DiNUtakOd1BcLQWEn3f", "category": "AI", "tags": []}, {"title": "redkaytop/framepack_comfyui · Cloud Native Build", "url": "https://cnb.cool/redkaytop/framepack_comfyui", "category": "AI", "tags": []}, {"title": "VIZINTZOR/F5-TTS-THAI · Hugging Face", "url": "https://huggingface.co/VIZINTZOR/F5-TTS-THAI", "category": "AI", "tags": ["Ai"]}, {"title": "Custom_OpenClash_Rules/cfg at main · Aethersailor/Custom_OpenClash_Rules", "url": "https://github.com/Aethersailor/Custom_OpenClash_Rules/tree/main/cfg", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "无意间发现了一个AI视频总结的工具/项目 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/678756", "category": "AI", "tags": ["Ai", "Linux", "工具"]}, {"title": "Cursor 软件开发 MCP工具组合推荐 - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/681924/2", "category": "AI", "tags": ["工具", "Linux"]}, {"title": "下一个SJSU，抓紧时间 - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/680339/384", "category": "AI", "tags": ["Linux"]}, {"title": "记录一下申请ccc学校具体流程，暂未下号，正在申请中！ - 福利羊毛 / 福利羊毛, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/686360", "category": "AI", "tags": ["Linux"]}, {"title": "Gemini2API 服务端实现，支持 RooCode（佬友共同维护版本） - 开发调优 / 开发调优, Lv2 - LINUX DO", "url": "https://linux.do/t/topic/575699", "category": "AI", "tags": ["Api", "Linux"]}, {"title": "MCP Feedback Enhanced 支持ssh remote、WSL，Cursor、Augment、Trae、Widsurf 等使用次数加倍 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/701931/21", "category": "AI", "tags": ["Linux"]}, {"title": "Cloudflare 临时邮件", "url": "https://mail.qiumail.ink/", "category": "AI", "tags": []}, {"title": "注册美国区Apple ID保姆级教程 - 知乎", "url": "https://zhuanlan.zhihu.com/p/623576755", "category": "AI", "tags": ["教程"]}, {"title": "jeromeleong/poe2openai: Convert Poe API into OpenAI format API", "url": "https://github.com/jeromeleong/poe2openai", "category": "AI", "tags": ["Api", "Ai", "GitHub"]}, {"title": "dbccccccc/ttsfm：TTSFM 是一个逆向工程的 API 服务器，它镜像 OpenAI 的 TTS 服务，为具有多种语音选项的文本到语音转换提供兼容的接口。 --- dbccccccc/ttsfm: TTSFM is a reverse-engineered API server that mirrors OpenAI's TTS service, providing a compatible interface for text-to-speech conversion with multiple voice options.", "url": "https://github.com/dbccccccc/ttsfm", "category": "AI", "tags": ["Api", "Ai", "GitHub"]}, {"title": "gordon123/ComfyUI-F5-TTS-EN：Efit TTS 的 ComfyUI 节点泰语很清楚，你可以试试。 --- gordon123/ComfyUI-F5-TTS-TH: ComfyUI node สำหรับ เอฟไฟท์ทีทีเอส ภาษาไทย ชัดแจ๋ว ทดลองดูได้ครับ", "url": "https://github.com/gordon123/ComfyUI-F5-TTS-TH", "category": "AI", "tags": ["GitHub"]}, {"title": "用AI写了一个套图爬虫 - 开发调优 / 开发调优, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/711707/3", "category": "AI", "tags": ["Ai", "Linux"]}, {"title": "OpenList 交互式管理脚本 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/724976", "category": "AI", "tags": ["Linux"]}, {"title": "Home · ChefKissInc/QEMUAppleSilicon Wiki · GitHub", "url": "https://github.com/ChefKissInc/QEMUAppleSilicon/wiki", "category": "AI", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "一叶轻舟", "url": "https://69.197.134.230/", "category": "AI", "tags": []}, {"title": "收件箱 - miludeerforest", "url": "https://email-web-app.j3.workers.dev/", "category": "AI", "tags": []}, {"title": "Home | ElevenLabs", "url": "https://elevenlabs.io/app/home", "category": "AI", "tags": []}, {"title": "border1px/JianYingProDraft", "url": "https://github.com/border1px/JianYingProDraft", "category": "AI", "tags": ["GitHub"]}, {"title": "【长期更新】Augment Agent 工具集标准化工作流提示词模板 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/693762", "category": "AI", "tags": ["工具", "Linux"]}, {"title": "GuanYixuan/pyJianYingDraft: 轻量、灵活、易上手的Python剪映草稿生成及导出工具，构建全自动化视频剪辑/混剪流水线", "url": "https://github.com/GuanYixuan/pyJianYingDraft?tab=readme-ov-file#%E5%85%B3%E9%94%AE%E5%B8%A7", "category": "AI", "tags": ["工具", "GitHub"]}, {"title": "v0 API 教程 - 福利羊毛 / 福利羊毛, Lv3 - LINUX DO", "url": "https://linux.do/t/topic/755343", "category": "AI", "tags": ["教程", "Api", "Linux"]}, {"title": "实现claude code自由！一键部署的Docker化claude-code-proxy解决方案 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/760680", "category": "AI", "tags": ["Linux"]}, {"title": "将 n8n 简单汉化了一版，顺便启用了企业版 - 资源荟萃 - LINUX DO", "url": "https://linux.do/t/topic/745531/36", "category": "AI", "tags": ["Linux"]}, {"title": "Any Router", "url": "https://anyrouter.top/console/token", "category": "AI", "tags": []}, {"title": "项目探索 - Linux Do CDK", "url": "https://cdk.linux.do/explore", "category": "AI", "tags": ["Linux"]}, {"title": "高级模型无限使用策略，可2api, 无预制提示词 - 福利羊毛 / 福利羊毛, Lv3 - LINUX DO", "url": "https://linux.do/t/topic/782855", "category": "AI", "tags": ["Api", "Linux"]}, {"title": "AICode/README_CN.md at main · sylearn/AICode", "url": "https://github.com/sylearn/AICode/blob/main/README_CN.md", "category": "AI", "tags": ["Ai", "GitHub"]}, {"title": "【github 开源】零成本1分钟部署，告别信息过载的多平台热点聚合工具(手机推送)，v2.0.0 刚更新(支持docker）快破1000⭐了 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/797132", "category": "AI", "tags": ["开源", "<PERSON><PERSON><PERSON>", "工具", "Linux"]}, {"title": "Kontext-DEV【先去除后替换产品】换场景 - RunningHub ComfyUI Workflow", "url": "https://www.runninghub.cn/post/1945660410626285569", "category": "AI", "tags": []}, {"title": "Cursor/Augment Code开发Rules - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/780085", "category": "AI", "tags": ["Linux"]}, {"title": "⚡ N8N Workflow Documentation", "url": "https://n8n-workflows-production-9cc4.up.railway.app/", "category": "AI", "tags": []}, {"title": "【T佬】GPT-Load 1.0正式版发布，支持多渠道Key池轮询代理服务！ - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/789409", "category": "AI", "tags": ["Linux"]}, {"title": "TSCarterJr/UnsecuredAPIKeys-OpenSource: The code base behind the [Former] UnsecuredAPIKeys.com", "url": "https://github.com/TSCarterJr/UnsecuredAPIKeys-OpenSource", "category": "AI", "tags": ["Api", "GitHub"]}, {"title": "Augment 又送福利了 - 搞七捻三 / 搞七捻三, Lv1 - LINUX DO", "url": "https://linux.do/t/topic/813755", "category": "AI", "tags": ["Linux"]}, {"title": "WIPDF", "url": "https://wipdf.vercel.app/", "category": "AI", "tags": []}, {"title": "yuaotian/go-augment-cleaner: 清理Augment缓存和生成设备SessionId/解决 VSCode、Cursor、JetBrains 系列 IDE 中 Augment 插件无法登录的问题（Sign in failed. If you have a firewall, please add）/将所有 d1-d20.api.augmentcode.com 域名统一指向延迟最低的服务器IP", "url": "https://github.com/yuaotian/go-augment-cleaner", "category": "AI", "tags": ["Api", "Ai", "GitHub"]}, {"title": "Gak<PERSON>No<PERSON><PERSON>/hajimi-king: 人人都是哈基米大王", "url": "https://github.com/GakkiNoOne/hajimi-king", "category": "AI", "tags": ["GitHub"]}, {"title": "〖⭐️ (Pro Max) LINUX DO二级及以上用户尊享顶级阅览室〗盘点那些评论量多的帖子 (目前整理的话题总评论量已超48000条,升3级和3级保级必备神器,已达成预期目标,停更) - 搞七捻三 - LINUX DO", "url": "https://linux.do/t/topic/329593", "category": "AI", "tags": ["Linux"]}, {"title": "不需要手动算倍率！最全的倍率集合 - 开发调优 - LINUX DO", "url": "https://linux.do/t/topic/819208/8", "category": "AI", "tags": ["Linux"]}, {"title": "tsxcw/mtab: Mtab书签导航程序 - 免费无广告的浏览器书签助手，多端同步、美观易用的在 线导航和书签工具，自主研发免费使用，帮助您高效管理 网页和应用，提升在线体验。", "url": "https://github.com/tsxcw/mtab", "category": "AI", "tags": ["工具", "GitHub"]}, {"title": "Google 翻译", "url": "https://translate.google.com/?hl=zh-CN", "category": "AI", "tags": []}, {"title": "0086-19076089329", "url": "https://po.givemestar.com/dashboard", "category": "AI", "tags": []}, {"title": "BigSeller - Orders", "url": "https://www.bigseller.pro/web/order/index.htm?status=new", "category": "AI", "tags": []}, {"title": "FastMoss-挖掘TikTok热销爆品、解析爆品打造案例", "url": "https://www.fastmoss.com/zh/e-commerce/search?page=1&l1_cid=16&l2_cid=909192&l3_cid=911752®ion=TH", "category": "AI", "tags": ["Tiktok"]}, {"title": "DuckDuckGo Email | Email Protection from DuckDuckGo", "url": "https://duckduckgo.com/email/settings/autofill", "category": "AI", "tags": ["Ai"]}, {"title": "Build | Google AI Studio", "url": "https://aistudio.google.com/app/apps/drive/169U2Al5556WX7bcWYdaPwHvzoAU7PqW_", "category": "AI", "tags": ["Ai"]}, {"title": "tcp ************:80", "url": "https://tcp.ping.pe/************:80", "category": "AI", "tags": []}, {"title": "X-WRT/OpenWrt/LEDE 固件下载", "url": "https://downloads.x-wrt.com/rom/", "category": "AI", "tags": []}, {"title": "金山文档 | WPS云文档", "url": "https://www.kdocs.cn/view/l/caSWWxarIWrZ", "category": "AI", "tags": []}, {"title": "跨境电商职场人发声平台，跨境黑名单，避坑表 - 若比邻网", "url": "https://www.ratecompany.org/", "category": "AI", "tags": ["电商"]}, {"title": "USTC IP Blacklist", "url": "https://blackip.ustc.edu.cn/intro.php", "category": "AI", "tags": []}, {"title": "2023年免费国内CDN有哪些?国内免费CDN小结 | SKY博客", "url": "https://www.sky350.com/1235.html", "category": "AI", "tags": []}, {"title": "Joker_WRT - 概况 - LuCI", "url": "http://*************:20080/cgi-bin/luci/", "category": "AI", "tags": []}, {"title": "柚坛社区", "url": "https://www.uotan.cn/", "category": "AI", "tags": []}, {"title": "GitHub - bepass-org/oblivion-desktop: Oblivion Desktop - Unofficial Warp Client for Windows/Mac/Linux", "url": "https://github.com/bepass-org/oblivion-desktop", "category": "AI", "tags": ["<PERSON><PERSON><PERSON>", "GitHub"]}, {"title": "如何将G<PERSON><PERSON><PERSON><PERSON> sim卡转换为esim | <PERSON>'s Blog", "url": "https://azhu.site/posts/1015/", "category": "AI", "tags": []}]}