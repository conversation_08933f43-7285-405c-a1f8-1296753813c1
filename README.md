# Chrome书签转JSON工具

这个工具可以将Chrome导出的书签HTML文件转换为适合导入其他项目的JSON格式。

## 功能特点

- ✅ 解析Chrome标准书签HTML格式
- ✅ 保持文件夹层级结构
- ✅ 自动提取标签和分类
- ✅ 支持多种输出格式
- ✅ 时间戳标准化处理
- ✅ 域名提取和分析
- ✅ 可选的图标数据处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
python bookmark_converter.py bookmarks.html
```

### 完整参数

```bash
python bookmark_converter.py input.html -o output.json --format full --include-icons
```

### 参数说明

- `input_file`: 输入的Chrome书签HTML文件
- `-o, --output`: 输出JSON文件名（默认：bookmarks.json）
- `--format`: 输出格式类型
  - `full`: 完整格式（包含所有字段和元数据）
  - `simple`: 精简格式（仅包含基本字段）
  - `grouped`: 按分类分组格式
- `--include-icons`: 包含base64编码的图标数据

## 输出格式说明

### 1. 完整格式 (full)

```json
{
  "metadata": {
    "source": "Chrome Bookmarks",
    "export_date": "2025-01-28T10:08:32Z",
    "total_bookmarks": 587,
    "total_categories": 15,
    "format_version": "1.0",
    "include_icons": false
  },
  "bookmarks": [
    {
      "id": "fd51a979c20d",
      "title": "FastMoss-TikTok短视频&直播电商与达人营销数据分析",
      "url": "https://www.fastmoss.com/zh/e-commerce/search",
      "description": "来自 www.fastmoss.com 的书签",
      "category": "Tiktok",
      "tags": ["电商", "Tiktok"],
      "date_added": "2024-07-15T11:19:26Z",
      "folder_path": ["书签栏", "电商", "Tiktok"],
      "domain": "www.fastmoss.com"
    }
  ],
  "categories": [
    {
      "name": "Tiktok",
      "parent": "电商",
      "path": ["书签栏", "电商", "Tiktok"],
      "bookmark_count": 106
    }
  ]
}
```

### 2. 精简格式 (simple)

```json
{
  "bookmarks": [
    {
      "title": "FastMoss-TikTok短视频&直播电商与达人营销数据分析",
      "url": "https://www.fastmoss.com/zh/e-commerce/search",
      "category": "Tiktok",
      "tags": ["Tiktok", "电商"]
    }
  ]
}
```

### 3. 分组格式 (grouped)

```json
{
  "metadata": { ... },
  "bookmarks_by_category": {
    "Tiktok": [
      { "书签数据..." }
    ],
    "AI": [
      { "书签数据..." }
    ]
  }
}
```

## 转换结果

从您的书签文件中成功提取了：

- **总书签数**: 587个
- **分类数**: 15个
- **主要分类**:
  - TikTok: 106个书签
  - AI: 110个书签
  - NAS: 97个书签
  - VPS: 75个书签
  - tool: 71个书签
  - android: 37个书签
  - 路由: 26个书签
  - unraid: 21个书签
  - 物流: 19个书签
  - media: 9个书签
  - 设计: 8个书签
  - 技术博客: 8个书签

## 生成的文件

1. **bookmarks_full.json** - 完整格式，包含所有字段和元数据
2. **bookmarks_simple.json** - 精简格式，仅包含基本信息
3. **bookmarks_grouped.json** - 按分类分组的格式

## 适用场景

这些JSON文件可以用于：

1. **导入其他书签管理工具**
2. **构建个人导航网站**
3. **数据分析和统计**
4. **备份和迁移**
5. **API接口数据源**
6. **搜索引擎构建**

## 技术特点

- 自动识别文件夹层级
- 智能标签提取（基于域名和标题）
- Unix时间戳转ISO格式
- 支持中文和特殊字符
- 错误处理和容错机制
- 可扩展的输出格式

## 注意事项

- 图标数据较大，建议根据需要选择是否包含
- 时间戳已转换为标准ISO格式
- 所有文本都保持UTF-8编码
- 生成的ID基于URL和标题的MD5哈希

## 自定义扩展

您可以根据需要修改脚本来：

- 添加新的输出格式
- 自定义标签提取规则
- 修改分类逻辑
- 添加数据验证
- 集成其他工具
